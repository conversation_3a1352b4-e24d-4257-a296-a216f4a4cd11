import { Types } from 'mongoose';
import { IUser } from '../models/User';

/**
 * Utility functions for calendar operations
 */

/**
 * Safely converts an ObjectId or string to a string
 * @param value The value to convert
 * @returns A string representation of the value
 */
export const toStringValue = (value: string | Types.ObjectId | undefined): string | undefined => {
  if (!value) return undefined;
  return value instanceof Types.ObjectId ? value.toString() : value;
};

/**
 * Gets the user's timezone or returns a default
 * @param user The user object
 * @param defaultTimezone Default timezone to use if user has none
 * @returns The user's timezone or the default
 */
export const getUserTimezone = (user: IUser, defaultTimezone = 'UTC'): string => {
  return user.preferences?.timezone || defaultTimezone;
};

/**
 * Safely handles Google OAuth token responses which may have different structures
 * @param response The response from Google OAuth
 * @returns The tokens object
 */
export const extractTokensFromResponse = (response: any): any => {
  return response.tokens || response;
};
