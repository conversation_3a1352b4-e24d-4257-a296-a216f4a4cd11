import { TaskModel, TaskPriority } from './types/task.model';

export interface CalendarEvent {
  id: string;
  title: string;
  start: Date | string;
  end?: Date | string;
  allDay?: boolean;
  extendedProps: {
    type: 'task' | 'event';
    taskId?: string;
    completed?: boolean;
    priority?: TaskPriority;
    description?: string;
    location?: string | { name: string };
    categories?: any[];
    reminders?: any[];
    syncStatus?: 'synced' | 'pending' | 'failed' | 'not_synced';
    syncProvider?: 'google' | 'outlook' | 'apple' | string;
    syncLastUpdated?: Date | string;
  };
}

export function mapTaskToCalendarEvent(task: TaskModel): CalendarEvent {
  // If deadline is a string, parse it to a Date
  const deadline = task.deadline ? new Date(task.deadline) : new Date();
  
  // For all-day events, set end to end of day
  const isAllDay = !task.deadline || 
                 (typeof task.deadline === 'string' && !task.deadline.includes('T'));
  
  let endTime = new Date(deadline);
  
  if (isAllDay) {
    // For all-day events, set end to end of day
    endTime.setHours(23, 59, 59, 999);
  } else {
    // For timed events, default to 1 hour duration
    endTime.setHours(deadline.getHours() + 1);
  }

  // Determine calendar sync status from task metadata
  let syncStatus: 'synced' | 'pending' | 'failed' | 'not_synced' = 'not_synced';
  let syncProvider: string | undefined = undefined;
  let syncLastUpdated: Date | string | undefined = undefined;
  
  // Check if task has calendar sync metadata
  if (task.metadata) {
    if (task.metadata.googleCalendarEventId) {
      syncStatus = task.metadata.calendarSyncStatus || 'synced';
      syncProvider = task.metadata.calendarSyncProvider || 'google';
      syncLastUpdated = task.metadata.calendarLastSyncedAt;
    } else if (task.metadata.calendarSyncStatus) {
      syncStatus = task.metadata.calendarSyncStatus;
      syncProvider = task.metadata.calendarSyncProvider;
      syncLastUpdated = task.metadata.calendarLastSyncedAt;
    }
  }
  
  // Check if task has direct calendarSync property (newer API format)
  if (task.calendarSync) {
    syncStatus = task.calendarSync.synced ? 'synced' : 'not_synced';
    syncProvider = task.calendarSync.provider;
    syncLastUpdated = task.calendarSync.lastSyncedAt;
  }

  return {
    id: task._id,
    title: task.title,
    start: deadline,
    end: endTime,
    allDay: isAllDay,
    extendedProps: {
      type: 'task',
      taskId: task._id,
      completed: task.completed,
      priority: task.priority || 'Medium',
      description: task.content || '',
      location: typeof task.location === 'string' 
        ? task.location 
        : task.location?.name || '',
      categories: Array.isArray(task.categories) ? task.categories : [],
      reminders: Array.isArray(task.reminders) ? task.reminders : [],
      syncStatus,
      syncProvider,
      syncLastUpdated
    }
  };
}

export function mapCalendarEventToTask(event: CalendarEvent): Partial<TaskModel> {
  return {
    _id: event.id,
    title: event.title,
    content: event.extendedProps.description,
    deadline: event.start instanceof Date ? event.start : new Date(event.start),
    priority: event.extendedProps.priority,
    completed: event.extendedProps.completed || false,
  };
}

// Helper to check if a date is an all-day date (no time component)
export function isAllDayDate(date: Date): boolean {
  return (
    date.getHours() === 0 &&
    date.getMinutes() === 0 &&
    date.getSeconds() === 0 &&
    date.getMilliseconds() === 0
  );
}

// Helper to format date for display
export function formatEventDate(date: Date | string, allDay: boolean): string {
  const dateObj = date instanceof Date ? date : new Date(date);
  
  if (allDay) {
    return dateObj.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  }
  
  return dateObj.toLocaleString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}
