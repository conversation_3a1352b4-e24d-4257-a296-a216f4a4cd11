'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useGrocery } from '@/lib/grocery-context';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Users,
  Share2,
  Clock,
  Check,
  X,
  Mail,
  Eye,
  Edit,
  Crown
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface SharedListsBrowserProps {
  className?: string;
}

// Utility function to get initials from name or email
const getInitials = (name?: string, email?: string): string => {
  if (name) {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  }
  if (email) {
    return email.slice(0, 2).toUpperCase();
  }
  return 'U';
};

export function SharedListsBrowser({ className }: SharedListsBrowserProps) {
  const {
    sharedLists,
    pendingInvitations,
    isLoadingCollaboration,
    switchToSharedList,
    switchToMyList,
    acceptInvitation,
    declineInvitation
  } = useGrocery();

  const { toast } = useToast();
  const [isInvitationsOpen, setIsInvitationsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSwitchToList = async (listOwnerId: string) => {
    try {
      await switchToSharedList(listOwnerId);
      toast({
        title: 'Switched to Shared List',
        description: 'You are now viewing a shared grocery list.',
      });
    } catch (error) {
      console.error('Failed to switch to shared list:', error);
      toast({
        title: 'Error',
        description: 'Failed to switch to the shared list.',
        variant: 'destructive',
      });
    }
  };

  const handleSwitchToMyList = async () => {
    try {
      await switchToMyList();
      toast({
        title: 'Switched to Your List',
        description: 'You are now viewing your own grocery list.',
      });
    } catch (error) {
      console.error('Failed to switch to own list:', error);
      toast({
        title: 'Error',
        description: 'Failed to switch to your own list.',
        variant: 'destructive',
      });
    }
  };

  const handleAcceptInvitation = async (token: string) => {
    try {
      setIsSubmitting(true);
      await acceptInvitation(token);
      toast({
        title: 'Invitation Accepted',
        description: 'You have successfully joined the shared grocery list.',
      });
    } catch (error) {
      console.error('Failed to accept invitation:', error);
      toast({
        title: 'Error',
        description: 'Failed to accept the invitation.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeclineInvitation = async (token: string) => {
    try {
      setIsSubmitting(true);
      await declineInvitation(token);
      toast({
        title: 'Invitation Declined',
        description: 'You have declined the invitation.',
      });
    } catch (error) {
      console.error('Failed to decline invitation:', error);
      toast({
        title: 'Error',
        description: 'Failed to decline the invitation.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'editor':
        return <Edit className="h-4 w-4 text-blue-500" />;
      case 'viewer':
        return <Eye className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'owner':
        return 'default';
      case 'editor':
        return 'secondary';
      case 'viewer':
        return 'outline';
      default:
        return 'outline';
    }
  };

  if (isLoadingCollaboration) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Shared Lists
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Shared Lists
          </div>
          {pendingInvitations.length > 0 && (
            <Dialog open={isInvitationsOpen} onOpenChange={setIsInvitationsOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="relative">
                  <Mail className="h-4 w-4 mr-2" />
                  Invitations
                  <Badge variant="destructive" className="ml-2 h-5 w-5 p-0 text-xs">
                    {pendingInvitations.length}
                  </Badge>
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Pending Invitations</DialogTitle>
                  <DialogDescription>
                    You have {pendingInvitations.length} pending invitation{pendingInvitations.length !== 1 ? 's' : ''}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  {pendingInvitations.map((invitation) => (
                    <div key={invitation._id} className="p-4 border rounded-lg space-y-3">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            {getInitials(invitation.inviterUserId.name, invitation.inviterUserId.email)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="text-sm font-medium">
                            {invitation.inviterUserId.name || invitation.inviterUserId.email}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            invited you as {invitation.role}
                          </div>
                        </div>
                        <Badge variant={getRoleBadgeVariant(invitation.role)}>
                          {invitation.role}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          onClick={() => handleAcceptInvitation(invitation.token)}
                          disabled={isSubmitting}
                          className="flex-1"
                        >
                          <Check className="h-4 w-4 mr-1" />
                          Accept
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeclineInvitation(invitation.token)}
                          disabled={isSubmitting}
                          className="flex-1"
                        >
                          <X className="h-4 w-4 mr-1" />
                          Decline
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </DialogContent>
            </Dialog>
          )}
        </CardTitle>
        <CardDescription>
          Browse and switch between shared grocery lists
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* My List */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Your List</h4>
          <div className="p-3 rounded-lg border bg-muted/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Avatar className="h-8 w-8">
                  <AvatarFallback>
                    {getInitials("You")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">My Grocery List</span>
                    <Crown className="h-4 w-4 text-yellow-500" />
                  </div>
                  <span className="text-xs text-muted-foreground">Your personal list</span>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSwitchToMyList}
              >
                View
              </Button>
            </div>
          </div>
        </div>

        {sharedLists.length > 0 && (
          <>
            <Separator />

            {/* Shared Lists */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium">
                Shared Lists ({sharedLists.length})
              </h4>
              <div className="space-y-3">
                {sharedLists.map((list) => (
                  <div key={list._id} className="p-3 rounded-lg border">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            {getInitials(
                              typeof list.userId === 'object' ? list.userId.name : undefined,
                              typeof list.userId === 'object' ? list.userId.email : undefined
                            )}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">
                              {list.name || `${
                                typeof list.userId === 'object'
                                  ? (list.userId.name || list.userId.email || 'Unknown')
                                  : 'Unknown'
                              }'s List`}
                            </span>
                            {getRoleIcon(list.userRole || 'viewer')}
                          </div>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>by {
                              typeof list.userId === 'object'
                                ? (list.userId.name || list.userId.email || 'Unknown')
                                : 'Unknown'
                            }</span>
                            <span>•</span>
                            <span>{list.collaboratorCount || 1} member{(list.collaboratorCount || 1) !== 1 ? 's' : ''}</span>
                            {list.lastCollaborativeActivity && (
                              <>
                                <span>•</span>
                                <span>
                                  <Clock className="h-3 w-3 inline mr-1" />
                                  {formatDistanceToNow(new Date(list.lastCollaborativeActivity), { addSuffix: true })}
                                </span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={getRoleBadgeVariant(list.userRole || 'viewer')}>
                          {list.userRole || 'viewer'}
                        </Badge>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSwitchToList(
                            typeof list.userId === 'object' ? list.userId._id : list.userId
                          )}
                        >
                          View
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {sharedLists.length === 0 && pendingInvitations.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No shared lists yet</p>
            <p className="text-xs">Ask someone to share their grocery list with you</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
