"use client"

import React from 'react'; // Re-added explicit React import
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Home, ListTodo, FileText, Star, Calendar, MapPin, Settings, PlusCircle,
  ChevronDown, ChevronRight, ShoppingCart, MoreHorizontal, Edit, Trash, Move, FolderPlus, ListFilter, CheckCircle2
} from "lucide-react" // Added CheckCircle2
import { ScrollArea } from "@/components/ui/scroll-area"
import { useRouter, usePathname } from 'next/navigation'; // Import useRouter and usePathname
import { useState, useEffect, useRef, useCallback } from "react"
import { categoryService, Category as FetchedCategory, CategoryDTO } from "../lib/category-service"
// Removed direct import: import { NewTaskDialog } from "./new-task-dialog"
import { toast } from "@/components/ui/use-toast";
import axios from "axios";
import { useDashboardContext } from '@/lib/dashboard-context'; // Import context hook
// Import the component and the exported props type
import EditCategoryDialog, { EditCategoryDialogProps } from "./edit-category-dialog";
import { Skeleton } from "@/components/ui/skeleton";

// Define the structure for categories received from the parent
export interface HierarchicalCategory {
  _id: string;
  name: string;
  parentCategory?: string | null;
  color?: string;
  isPredefined?: boolean;
  isAiGenerated?: boolean;
  allowUserManagement?: boolean;
  taskCount?: number;
  subcategories?: HierarchicalCategory[];
}

// Define props for CategorySidebar
interface CategorySidebarProps {
  categories: HierarchicalCategory[];
  isLoading: boolean;
  error: string | null;
  activeContextId: string | null; // Can be category ID, null (All), or COMPLETED_VIEW_CONTEXT_ID
  completedTaskCount: number; // Add prop for completed count
  uncategorizedTaskCount: number; // Add prop for uncategorized count
  onCategorySelect: (categoryId: string | null) => void;
  onShowAll: () => void;
  onShowCompleted: () => void; // Add handler for showing completed
  onShowUncategorized: () => void; // Add handler for showing uncategorized
  onTaskAdded: (task: any) => void;
  onCategoryUpdated: () => void;
  className?: string; // Add optional className prop
  
  // Calendar specific props (optional)
  isCalendarPage?: boolean;
  selectedCalendarDate?: Date;
  onCalendarDateSelect?: (date: Date) => void;
  calendarTasks?: any[];
  selectedCalendarCategories?: string[];
  onCalendarCategoryToggle?: (categoryId: string) => void;
  onCalendarTaskClick?: (task: any) => void;
}

// Define the special ID used by the parent for the completed view context
const COMPLETED_VIEW_CONTEXT_ID = 'completed_tasks_view';
// Define the special ID used by the parent for the uncategorized view context
const UNCATEGORIZED_VIEW_CONTEXT_ID = 'uncategorized_tasks_view';

// Define available colors
const availableColors = [
    '#3B82F6', '#60A5FA', '#2563EB', '#1E40AF', '#A855F7', '#8B5CF6', '#7C3AED', '#6D28D9',
    '#22C55E', '#4ADE80', '#16A34A', '#15803D', '#EF4444', '#F87171', '#DC2626', '#B91C1C',
    '#EAB308', '#F59E0B', '#F97316', '#EA580C', '#EC4899', '#F472B6', '#DB2777', '#BE185D',
    '#14B8A6', '#2DD4BF', '#0D9488', '#0F766E', '#6366F1', '#818CF8', '#4F46E5', '#4338CA',
    '#71717A', '#52525B', '#3F3F46', '#27272A'
];
const defaultColorHex = '#71717A';

// Map hex to Tailwind class
const hexToTailwindMap: { [key: string]: string } = {
    '#3B82F6': 'bg-blue-500', '#60A5FA': 'bg-blue-400', '#2563EB': 'bg-blue-600', '#1E40AF': 'bg-blue-700',
    '#A855F7': 'bg-purple-500', '#8B5CF6': 'bg-violet-500', '#7C3AED': 'bg-violet-600', '#6D28D9': 'bg-violet-700',
    '#22C55E': 'bg-green-500', '#4ADE80': 'bg-green-400', '#16A34A': 'bg-green-600', '#15803D': 'bg-green-700',
    '#EF4444': 'bg-red-500', '#F87171': 'bg-red-400', '#DC2626': 'bg-red-600', '#B91C1C': 'bg-red-700',
    '#EAB308': 'bg-yellow-500', '#F59E0B': 'bg-amber-500', '#F97316': 'bg-orange-500', '#EA580C': 'bg-orange-600',
    '#EC4899': 'bg-pink-500', '#F472B6': 'bg-pink-400', '#DB2777': 'bg-pink-600', '#BE185D': 'bg-pink-700',
    '#14B8A6': 'bg-teal-500', '#2DD4BF': 'bg-teal-400', '#0D9488': 'bg-teal-600', '#0F766E': 'bg-teal-700',
    '#6366F1': 'bg-indigo-500', '#818CF8': 'bg-indigo-400', '#4F46E5': 'bg-indigo-600', '#4338CA': 'bg-indigo-700',
    '#71717A': 'bg-zinc-500', '#52525B': 'bg-zinc-600', '#3F3F46': 'bg-zinc-700', '#27272A': 'bg-zinc-800'
};
const defaultColorClass = "bg-zinc-500";


export default function CategorySidebar({
    categories,
    isLoading,
    error,
    activeContextId,
    completedTaskCount, // Destructure new prop
    uncategorizedTaskCount, // Destructure new prop
    onCategorySelect,
    onShowAll,
    onShowCompleted, // Destructure new handler
    onShowUncategorized, // Destructure new handler
    onTaskAdded,
    onCategoryUpdated,
    className, // Destructure className
    // Calendar specific props
    isCalendarPage = false,
    selectedCalendarDate,
    onCalendarDateSelect,
    calendarTasks = [],
    selectedCalendarCategories = [],
    onCalendarCategoryToggle,
    onCalendarTaskClick
}: CategorySidebarProps): React.ReactElement { // Changed return type to React.ReactElement

  const { openNewTaskDialog } = useDashboardContext(); // Get context function
  const router = useRouter(); // Initialize useRouter
  const pathname = usePathname(); // Get current pathname
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState("");
  const [isAddingSubcategory, setIsAddingSubcategory] = useState<string | null>(null);
  const [newSubcategoryName, setNewSubcategoryName] = useState("");
  const [editingCategory, setEditingCategory] = useState<HierarchicalCategory | null>(null);

  const categoryInputRef = useRef<HTMLInputElement>(null);
  const subcategoryInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => { if (isAddingCategory && categoryInputRef.current) categoryInputRef.current.focus(); }, [isAddingCategory]);

  // Enhanced effect for subcategory input focus
  useEffect(() => {
    if (isAddingSubcategory && subcategoryInputRef.current) {
      // Focus immediately
      subcategoryInputRef.current.focus();

      // Set up an interval to keep focusing for a short period
      const focusInterval = setInterval(() => {
        if (subcategoryInputRef.current) {
          subcategoryInputRef.current.focus();
        } else {
          clearInterval(focusInterval);
        }
      }, 50); // Check every 50ms

      // Clear the interval after 500ms
      setTimeout(() => clearInterval(focusInterval), 500);

      // Clean up function
      return () => clearInterval(focusInterval);
    }
  }, [isAddingSubcategory]);

  const toggleCategoryExpanded = useCallback((categoryId: string) => {
    setExpandedCategories((prev) => ({ ...prev, [categoryId]: !prev[categoryId] }));
  }, []);

  const getCategoryDotColorClass = (hexColor: string | undefined): string => {
      if (typeof hexColor !== 'string') return defaultColorClass;
      return hexToTailwindMap[hexColor.toUpperCase()] || defaultColorClass;
  };

  const handleAddCategoryClick = () => { setIsAddingCategory(true); setIsAddingSubcategory(null); };
  const handleCancelAddCategory = () => { setIsAddingCategory(false); setNewCategoryName(""); };
  const handleSaveNewCategory = async () => {
    const trimmedName = newCategoryName.trim();
    if (!trimmedName) return;
    const currentTopLevelColors = new Set(categories.filter(c => !c.parentCategory).map(c => c.color?.toUpperCase()));
    let nextColorHex = availableColors.find(hex => !currentTopLevelColors.has(hex.toUpperCase())) || defaultColorHex;
    try {
      await categoryService.createCategory({ name: trimmedName, parentCategory: null, color: nextColorHex });
      toast({ title: "Category Created", description: `"${trimmedName}" added.` });
      handleCancelAddCategory();
      onCategoryUpdated?.();
    } catch (error: any) {
      console.error("Failed to create category:", error);
      toast({ title: "Error", description: error.response?.data?.message || "Could not create category.", variant: "destructive" });
    }
  };

   const handleAddSubcategoryClick = (parentId: string) => {
     setIsAddingSubcategory(parentId);
     setIsAddingCategory(false);
     setNewSubcategoryName("");

     if (!expandedCategories[parentId]) {
         toggleCategoryExpanded(parentId);
     }
   };
   const handleCancelAddSubcategory = () => { setIsAddingSubcategory(null); setNewSubcategoryName(""); };
   const handleSaveNewSubcategory = async () => {
     const trimmedName = newSubcategoryName.trim();
     if (!trimmedName || !isAddingSubcategory) return;
     const defaultSubColorHex = '#808080';
     try {
       await categoryService.createCategory({ name: trimmedName, parentCategory: isAddingSubcategory, color: defaultSubColorHex });
       toast({ title: "Subcategory Created", description: `"${trimmedName}" added.` });
       handleCancelAddSubcategory();
       onCategoryUpdated?.();
     } catch (error: any) {
       console.error("Failed to create subcategory:", error);
       toast({ title: "Error", description: error.response?.data?.message || "Could not create subcategory.", variant: "destructive" });
     }
   };

  const deleteCategory = async (categoryId: string) => {
    // Validate the category ID before proceeding
    if (!categoryId || categoryId === 'undefined') {
      toast({
        title: "Deletion Failed",
        description: "Invalid category ID. Please try again or refresh the page.",
        variant: "destructive"
      });
      return;
    }

    if (!window.confirm("Are you sure you want to delete this category? This cannot be undone.")) return;

    try {
      await categoryService.deleteCategory(categoryId);
      toast({ title: "Category Deleted" });
      onCategoryUpdated?.();
    } catch (error) {
      console.error("Failed to delete category:", error);
      let message = "Could not delete category.";
      if (axios.isAxiosError(error) && error.response?.data?.message) {
          message = error.response.data.message;
      } else if (error instanceof Error) {
          message = error.message;
      }
      toast({ title: "Deletion Failed", description: message, variant: "destructive" });
    }
  };

  const handleEditCategoryClick = (category: HierarchicalCategory) => {
      setEditingCategory(category);
  };

  const handleCloseEditDialog = (refresh?: boolean) => {
      setEditingCategory(null);
      if (refresh) {
          onCategoryUpdated?.();
      }
  };

  const renderCategory = (category: HierarchicalCategory, level: number = 0): React.ReactElement => { // Changed return type to React.ReactElement
    const isExpanded = expandedCategories[category._id] || false;
    const hasChildren = category.subcategories && category.subcategories.length > 0;
    const isActiveContext = activeContextId === category._id;
    // Adjust indentation size and line positioning
    const indentSizeRem = 1.25; // Increase indentation slightly
    const lineOffsetRem = 0.625; // Position line roughly in the middle of the indent

    return (
     <div key={category._id} className="relative group category-item" style={{ paddingLeft: `${level * indentSizeRem}rem` }}>
       {/* Vertical connecting line for levels > 0 */}
       {level > 0 && <div className="absolute left-0 top-0 bottom-0 w-px bg-white/10" style={{ left: `${(level * indentSizeRem) - lineOffsetRem}rem` }}></div>}
       {/* Horizontal connecting line */}
       {level > 0 && <div className="absolute left-0 top-1/2 h-px w-2 bg-white/10" style={{ left: `${(level * indentSizeRem) - lineOffsetRem}rem`, transform: 'translateY(-50%)' }}></div>}

       <div className={`flex items-center rounded-md ${isActiveContext ? 'bg-blue-500/20' : 'hover:bg-white/5'}`}>
         <Button
           variant="ghost"
           className={`w-full justify-start text-left font-normal h-8 px-2 ${isActiveContext ? 'text-blue-300' : 'text-white/80 hover:text-white'}`}
           size="sm"
           onClick={() => {
             console.log("[CategorySidebar] Category clicked - ID:", category._id, "Name:", category.name);
             onCategorySelect(category._id);
           }}
           title={category.name}
         >
           <span className="w-5 mr-1 flex-shrink-0 flex items-center justify-center" onClick={(e) => { e.stopPropagation(); if(hasChildren) toggleCategoryExpanded(category._id); }}>
             {hasChildren ? (
               isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
             ) : (
               <span className="w-4"></span>
             )}
           </span>
           {level === 0 && <div className={`h-2 w-2 rounded-full mr-2 flex-shrink-0 ${getCategoryDotColorClass(category.color)}`} />}
           <span className="truncate flex-grow">{category.name}</span>
           {category.taskCount !== undefined && category.taskCount > 0 && (
             <span className={`ml-auto text-xs font-mono px-1.5 py-0.5 rounded ${isActiveContext ? 'bg-blue-600/50 text-blue-100' : 'bg-white/10 text-white/60'}`}>
               {category.taskCount}
             </span>
           )}
         </Button>
         <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className={`h-7 w-7 opacity-0 group-hover:opacity-100 hover:opacity-100 flex-shrink-0 ${isActiveContext ? 'text-blue-300 hover:bg-blue-500/30' : 'text-white/60 hover:text-white hover:bg-white/10'}`} onClick={(e) => e.stopPropagation()} title="More Actions">
                    <MoreHorizontal className="h-4 w-4" />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48 bg-card border-white/5" onClick={(e) => e.stopPropagation()}>
                <DropdownMenuItem onSelect={() => handleAddSubcategoryClick(category._id)}>
                    <FolderPlus className="mr-2 h-4 w-4" /> <span>Add Subcategory</span>
                </DropdownMenuItem>
                <DropdownMenuItem onSelect={() => handleEditCategoryClick(category)}>
                    <Edit className="mr-2 h-4 w-4" /> <span>Edit</span>
                </DropdownMenuItem>
                <DropdownMenuItem disabled>
                    <Move className="mr-2 h-4 w-4" /> <span>Move</span>
                </DropdownMenuItem>
                {!category.isPredefined && (
                    <>
                        <DropdownMenuSeparator className="bg-white/10" />
                        <DropdownMenuItem
                            onSelect={() => {
                                // Ensure category._id is defined before attempting to delete
                                if (category._id && category._id !== 'undefined') {
                                    deleteCategory(category._id);
                                } else {
                                    toast({
                                        title: "Deletion Failed",
                                        description: "Invalid category ID. Please try again or refresh the page.",
                                        variant: "destructive"
                                    });
                                }
                            }}
                            className="text-red-400 focus:text-red-400 focus:bg-red-500/10"
                        >
                            <Trash className="mr-2 h-4 w-4" /> <span>Delete</span>
                        </DropdownMenuItem>
                    </>
                )}
            </DropdownMenuContent>
         </DropdownMenu>
       </div>
       {isAddingSubcategory === category._id && (
          <div className="mt-1 pr-1 py-1" style={{ paddingLeft: `${(level + 1) * indentSizeRem}rem` }}> {/* Indent add form */}
             <div className="flex items-center gap-1">
               <Input
                 ref={subcategoryInputRef}
                 type="text"
                 value={newSubcategoryName}
                 onChange={(e) => setNewSubcategoryName(e.target.value)}
                 onKeyDown={(e) => e.key === 'Enter' && handleSaveNewSubcategory()}
                 autoFocus
                 onBlur={() => {
                   // Add a small delay before canceling to allow for refocusing
                   setTimeout(() => {
                     // Only cancel if the input is still empty and we're not focused on it
                     if (!newSubcategoryName && document.activeElement !== subcategoryInputRef.current) {
                       handleCancelAddSubcategory();
                     }
                   }, 200);
                 }}
                 className="flex-1 bg-background/50 border border-white/10 rounded px-2 py-1 text-sm h-8"
                 placeholder="Subcategory name"
               />
               <Button variant="ghost" size="icon" className="h-6 w-6 text-green-500 flex-shrink-0" onClick={handleSaveNewSubcategory} title="Save">
                 <PlusCircle className="h-4 w-4" />
               </Button>
               <Button variant="ghost" size="icon" className="h-6 w-6 text-red-400 flex-shrink-0" onClick={handleCancelAddSubcategory} title="Cancel">
                 <Trash className="h-4 w-4" />
               </Button>
             </div>
          </div>
        )}
       {isExpanded && hasChildren && (
         <div className="mt-1 space-y-0.5 relative">
           {/* Vertical line connecting children */}
           <div className="absolute left-0 top-0 bottom-0 w-px bg-white/10" style={{ left: `${(level * indentSizeRem) + lineOffsetRem}rem` }}></div>
           {category.subcategories?.map(subcategory => (
             <React.Fragment key={subcategory._id}>
               {renderCategory(subcategory, level + 1)}
             </React.Fragment>
           ))}
         </div>
       )}
     </div>
    );
  };

  // Prepare props for EditCategoryDialog safely
  const editDialogCategoryProp = editingCategory ? {
      _id: editingCategory._id,
      name: editingCategory.name,
      color: editingCategory.color,
      isPredefined: editingCategory.isPredefined
  } : null;

  // Combine default classes with the passed className
  const combinedClassName = `fixed top-16 left-0 h-[calc(100vh-4rem)] w-64 z-30 overflow-y-auto bg-card border-r border-white/5 ${className || ''}`.trim();

  // Check if we're on the main dashboard page (not locations or groceries)
  const isMainDashboard = pathname === '/dashboard';

  return (
    <div className={combinedClassName}>
      <div className="p-4">
        {/* Replace direct dialog render with a trigger button using context */}
        <Button
          className="w-full justify-start text-left bg-blue-500 hover:bg-blue-600 text-white"
          size="sm"
          onClick={() => openNewTaskDialog()} // Call context function
        >
          <PlusCircle className="mr-2 h-4 w-4" />
          New Task
        </Button>
      </div>
      <ScrollArea className="flex-1 px-3">
        {/* Main Navigation Section */}
         <div className="py-2 space-y-1">
            <Button
              variant="ghost"
              className={`w-full justify-start text-left font-normal h-8 px-2 ${(activeContextId === null && pathname === '/dashboard') ? 'bg-blue-500/20 text-blue-300' : 'text-white/80 hover:text-white hover:bg-white/5'}`}
              size="sm"
              onClick={() => router.push('/dashboard')}
              title="Dashboard"
            >
               <Home className="h-4 w-4 mr-2 flex-shrink-0 text-blue-500" />
              <span className="truncate flex-grow">Dashboard</span>
           </Button>
           <Button
             variant="ghost"
             className={`w-full justify-start text-left font-normal h-8 px-2 ${activeContextId === 'locations' ? 'bg-blue-500/20 text-blue-300' : 'text-white/80 hover:text-white hover:bg-white/5'}`} // Assuming 'locations' context ID for active state
             size="sm"
             onClick={() => router.push('/dashboard/locations')} // Add routing logic for Locations
             title="Locations"
           >
              <MapPin className="h-4 w-4 mr-2 flex-shrink-0 text-purple-500" />
              <span className="truncate flex-grow">Locations</span>
           </Button>
           <Button
             variant="ghost"
             className={`w-full justify-start text-left font-normal h-8 px-2 ${pathname === '/dashboard/groceries' ? 'bg-blue-500/20 text-blue-300' : 'text-white/80 hover:text-white hover:bg-white/5'}`}
             size="sm"
             onClick={() => router.push('/dashboard/groceries')}
             title="Groceries"
           >
              <ShoppingCart className="h-4 w-4 mr-2 flex-shrink-0 text-green-500" />
              <span className="truncate flex-grow">Groceries</span>
           </Button>
           <Button
             variant="ghost"
             className={`w-full justify-start text-left font-normal h-8 px-2 ${pathname === '/calendar' ? 'bg-blue-500/20 text-blue-300' : 'text-white/80 hover:text-white hover:bg-white/5'}`}
             size="sm"
             onClick={() => router.push('/calendar')}
             title="Calendar"
           >
              <Calendar className="h-4 w-4 mr-2 flex-shrink-0 text-yellow-500" />
              <span className="truncate flex-grow">Calendar</span>
           </Button>
        </div>

        <hr className="border-white/10 my-1" />

        {/* Categories Section - Only show on main dashboard */}
        {isMainDashboard && (
          <div className="py-2">
            <div className="flex items-center justify-between px-1 mb-2">
              <h3 className="text-xs font-semibold text-white/50 uppercase tracking-wider">Categories</h3>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6 text-white/50 hover:text-white hover:bg-white/5"
                onClick={handleAddCategoryClick}
                title="Add Category"
              >
                <PlusCircle className="h-4 w-4" />
              </Button>
            </div>
            {isAddingCategory && (
              <div className="px-1 mb-2">
                <div className="flex items-center gap-1">
                  <Input
                    ref={categoryInputRef}
                    type="text"
                    value={newCategoryName}
                    onChange={(e) => setNewCategoryName(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSaveNewCategory()}
                    onBlur={() => !newCategoryName && handleCancelAddCategory()}
                    className="flex-1 bg-background/50 border border-white/10 rounded px-2 py-1 text-sm h-7"
                    placeholder="New category name"
                  />
                  <Button variant="ghost" size="icon" className="h-7 w-7 text-green-500 flex-shrink-0" onClick={handleSaveNewCategory} title="Save">
                    <PlusCircle className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="icon" className="h-7 w-7 text-red-400 flex-shrink-0" onClick={handleCancelAddCategory} title="Cancel">
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
            <div className="space-y-0.5">
               <Button
                 variant="ghost"
                 className={`w-full justify-start text-left font-normal h-8 px-2 ${activeContextId === null && pathname === '/dashboard' ? 'bg-blue-500/20 text-blue-300' : 'text-white/80 hover:text-white hover:bg-white/5'}`}
                 size="sm"
                 onClick={onShowAll}
               >
                  <ListFilter className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="truncate flex-grow">All Tasks</span>
               </Button>
               <hr className="border-white/10 my-1" />

              {isLoading && (
                  <>
                      <Skeleton key="skeleton-1" className="h-8 w-full mb-1" />
                      <Skeleton key="skeleton-2" className="h-8 w-full mb-1" />
                      <Skeleton key="skeleton-3" className="h-8 w-full" />
                  </>
              )}
              {error && <p className="px-1 text-sm text-red-500">{error}</p>}
              {!isLoading && !error && categories.map(category => (
                <React.Fragment key={category._id}>
                  {renderCategory(category)}
                </React.Fragment>
              ))}
            </div>

            {/* Uncategorized Tasks Section */}
            <hr className="border-white/10 my-2" />
            <div className="px-1 mb-2">
               <Button
                 variant="ghost"
                 className={`w-full justify-start text-left font-normal h-8 px-2 ${activeContextId === UNCATEGORIZED_VIEW_CONTEXT_ID ? 'bg-zinc-500/20 text-zinc-300' : 'text-white/80 hover:text-white hover:bg-white/5'}`}
                 size="sm"
                 onClick={onShowUncategorized}
                 title="Show Uncategorized Tasks"
               >
                  <ListTodo className="h-4 w-4 mr-2 flex-shrink-0 text-zinc-500" />
                  <span className="truncate flex-grow">Uncategorized</span>
                  {uncategorizedTaskCount > 0 && (
                    <span className={`ml-auto text-xs font-mono px-1.5 py-0.5 rounded ${activeContextId === UNCATEGORIZED_VIEW_CONTEXT_ID ? 'bg-zinc-600/50 text-zinc-100' : 'bg-white/10 text-white/60'}`}>
                      {uncategorizedTaskCount}
                    </span>
                  )}
               </Button>
            </div>

            {/* Completed Tasks Section */}
            <hr className="border-white/10 my-2" />
            <div className="px-1 mb-2">
               <Button
                 variant="ghost"
                 className={`w-full justify-start text-left font-normal h-8 px-2 ${activeContextId === COMPLETED_VIEW_CONTEXT_ID ? 'bg-green-500/20 text-green-300' : 'text-white/80 hover:text-white hover:bg-white/5'}`}
                 size="sm"
                 onClick={onShowCompleted} // Use the passed handler
                 title="Show Completed Tasks"
               >
                  <CheckCircle2 className="h-4 w-4 mr-2 flex-shrink-0 text-green-500" />
                  <span className="truncate flex-grow">Completed</span>
                  {completedTaskCount > 0 && (
                    <span className={`ml-auto text-xs font-mono px-1.5 py-0.5 rounded ${activeContextId === COMPLETED_VIEW_CONTEXT_ID ? 'bg-green-600/50 text-green-100' : 'bg-white/10 text-white/60'}`}>
                      {completedTaskCount}
                    </span>
                  )}
               </Button>
            </div>
          </div>
        )}
      </ScrollArea>

      {/* Calendar Controls - Only displayed on Calendar Page */}
      {isCalendarPage && (
        <div className="border-t border-white/5 p-4 space-y-4 overflow-y-auto flex-grow">
          <h3 className="font-medium text-sm text-white/80 mb-2">Calendar Controls</h3>

          {/* Calendar Sync Section */}
          <div className="space-y-2">
            <h4 className="text-xs font-medium text-white/60">Calendar Sync</h4>
            <Button 
              variant="outline" 
              size="sm"
              className="w-full justify-start text-xs"
              onClick={() => {}}
            >
              <Calendar className="mr-2 h-3 w-3" />
              Connect Calendar
            </Button>
          </div>

          {/* Categories Section */}
          {categories && categories.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-xs font-medium text-white/60">Calendar Categories</h4>
              {categories.map(category => (
                <div key={`cal-${category._id}`} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={`cal-category-${category._id}`}
                    checked={selectedCalendarCategories?.includes(category._id) || false}
                    onChange={() => onCalendarCategoryToggle?.(category._id)}
                    className="h-3 w-3"
                  />
                  <label
                    htmlFor={`cal-category-${category._id}`}
                    className="text-xs text-white/80 cursor-pointer"
                  >
                    {category.name}
                  </label>
                </div>
              ))}
            </div>
          )}

          {/* Upcoming Tasks Section */}
          {calendarTasks && calendarTasks.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-xs font-medium text-white/60">Upcoming Tasks</h4>
              <div className="space-y-1">
                {calendarTasks.map(task => (
                  <Button
                    key={task.id}
                    variant="ghost"
                    className="w-full justify-start text-left text-xs py-1 px-2 h-auto"
                    onClick={() => onCalendarTaskClick?.(task)}
                  >
                    <div className="flex flex-col gap-0.5">
                      <span className="text-xs font-medium truncate">{task.title}</span>
                      {task.dueDate && (
                        <span className="text-[10px] text-white/60">
                          {new Date(task.dueDate).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Settings button moved to bottom of sidebar */}
      <div className="border-t border-white/5 p-4">
        <Button
          variant="ghost"
          className="w-full justify-start text-left text-white/90 hover:text-white hover:bg-white/5"
          size="sm"
          onClick={() => router.push('/settings')}
        >
          <Settings className="mr-2 h-4 w-4 text-blue-500" /> Settings
        </Button>
      </div>
      {/* Render Edit Category Dialog conditionally */}
      <EditCategoryDialog
          category={editDialogCategoryProp} // Pass the prepared prop
          isOpen={!!editingCategory} // Boolean based on state
          onClose={handleCloseEditDialog}
      />
    </div>
  );
}
