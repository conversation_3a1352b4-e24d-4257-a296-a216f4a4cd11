import express from 'express';
import { protect } from '../../middleware/auth';
import { googleCalendarService } from '../../services/googleCalendarService';
import { Task } from '../../models/Task';
import { User, IUser } from '../../models/User';
import secureLogger from '../../utils/secureLogger';

const router = express.Router();

// @route   POST /api/v2/calendar/sync/task
// @desc    Sync a task with the connected calendar
// @access  Private
router.post('/sync/task', protect, async (req, res) => {
  try {
    const { taskId, title, description, startTime, endTime, allDay, location } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: { message: 'User not authenticated' }
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' }
      });
    }

    // Find the task
    const task = await Task.findOne({ _id: taskId, userId: user._id });
    if (!task) {
      return res.status(404).json({
        success: false,
        error: { message: 'Task not found' }
      });
    }

    // If task already has a calendar event ID, update it
    if (task.calendarEventId) {
      const updated = await googleCalendarService.updateEvent(user, task, task.calendarEventId);
      if (updated) {
        return res.json({
          success: true,
          eventId: task.calendarEventId,
          status: 'synced'
        });
      }
    }

    // Otherwise, create a new event
    const eventId = await googleCalendarService.createEvent(user, task);
    if (eventId) {
      // Update task with the new event ID
      task.calendarEventId = eventId;
      task.calendarSyncStatus = 'synced';
      task.calendarLastSyncedAt = new Date();
      await task.save();

      return res.json({
        success: true,
        eventId,
        status: 'synced'
      });
    }

    throw new Error('Failed to sync task with calendar');
  } catch (error: any) {
    secureLogger.error('[CalendarSync] Failed to sync task:', error);

    // Update task sync status if it exists
    if (req.body.taskId) {
      await Task.updateOne(
        { _id: req.body.taskId },
        {
          calendarSyncStatus: 'failed',
          calendarLastError: error.message
        }
      );
    }

    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to sync task with calendar',
        details: error.message
      }
    });
    return;
  }
});

// @route   DELETE /api/v2/calendar/sync/event/:eventId
// @desc    Remove an event from the calendar
// @access  Private
router.delete('/sync/event/:eventId', protect, async (req, res) => {
  try {
    const { eventId } = req.params;
    const { taskId } = req.query;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: { message: 'User not authenticated' }
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' }
      });
    }

    // If taskId is provided, update the task's sync status
    if (taskId) {
      await Task.updateOne(
        { _id: taskId, userId: user._id },
        {
          calendarSyncStatus: 'not_synced',
          calendarEventId: null,
          calendarLastSyncedAt: new Date()
        }
      );
    }

    // Delete the event from the calendar
    const success = await googleCalendarService.deleteEvent(user, eventId);

    if (success) {
      return res.json({ success: true });
    }

    throw new Error('Failed to delete event from calendar');
  } catch (error: any) {
    secureLogger.error('[CalendarSync] Failed to delete event:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to delete event from calendar',
        details: error.message
      }
    });
    return;
  }
});

// @route   GET /api/v2/calendar/sync/status/:taskId
// @desc    Get sync status for a task
// @access  Private
router.get('/sync/status/:taskId', protect, async (req, res) => {
  try {
    const { taskId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: { message: 'User not authenticated' }
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' }
      });
    }

    const task = await Task.findOne({ _id: taskId, userId: user._id });
    if (!task) {
      return res.status(404).json({
        success: false,
        error: { message: 'Task not found' }
      });
    }

    // If task has a calendar event ID, verify it still exists
    if (task.calendarEventId) {
      try {
        // This would typically involve checking the calendar API
        // For now, we'll assume the event exists if we have an ID
        return res.json({
          success: true,
          status: 'synced',
          eventId: task.calendarEventId,
          lastSyncedAt: task.calendarLastSyncedAt
        });
      } catch (error) {
        // Event might have been deleted externally
        task.calendarSyncStatus = 'not_synced';
        task.calendarEventId = undefined;
        task.calendarLastError = 'Event not found in calendar';
        await task.save();
      }
    }

    res.json({
      success: true,
      status: task.calendarSyncStatus || 'not_synced',
      lastSyncedAt: task.calendarLastSyncedAt
    });
    return;
  } catch (error: any) {
    secureLogger.error('[CalendarSync] Failed to get sync status:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get sync status',
        details: error.message
      }
    });
    return;
  }
});

// @route   POST /api/v2/calendar/sync/status/batch
// @desc    Get sync status for multiple tasks
// @access  Private
router.post('/sync/status/batch', protect, async (req, res) => {
  try {
    const { taskIds } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: { message: 'User not authenticated' }
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' }
      });
    }

    if (!Array.isArray(taskIds)) {
      return res.status(400).json({
        success: false,
        error: { message: 'taskIds must be an array' }
      });
    }

    const tasks = await Task.find({
      _id: { $in: taskIds },
      userId: user._id
    });

    const statusMap: Record<string, any> = {};

    tasks.forEach(task => {
      statusMap[(task._id as any).toString()] = {
        status: task.calendarSyncStatus || 'not_synced',
        eventId: task.calendarEventId,
        lastSyncedAt: task.calendarLastSyncedAt
      };
    });

    res.json({
      success: true,
      data: statusMap
    });
    return;
  } catch (error: any) {
    secureLogger.error('[CalendarSync] Failed to get batch sync status:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get batch sync status',
        details: error.message
      }
    });
    return;
  }
});

// @route   PUT /api/v2/calendar/sync/settings
// @desc    Update calendar sync settings
// @access  Private
router.put('/sync/settings', protect, async (req, res) => {
  try {
    const { syncEnabled, defaultCalendarId, syncPastEvents, syncReminders } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: { message: 'User not authenticated' }
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' }
      });
    }

    // Update user's calendar settings
    const updates: any = {};

    if (typeof syncEnabled === 'boolean') {
      updates.googleCalendarSyncEnabled = syncEnabled;
    }

    if (defaultCalendarId) {
      updates.googleCalendarId = defaultCalendarId;
    }

    if (typeof syncPastEvents === 'boolean') {
      updates.calendarSyncPastEvents = syncPastEvents;
    }

    if (typeof syncReminders === 'boolean') {
      updates.calendarSyncReminders = syncReminders;
    }

    await User.findByIdAndUpdate(user._id, { $set: updates });

    res.json({
      success: true,
      data: {
        syncEnabled: syncEnabled ?? user.googleCalendarSyncEnabled,
        defaultCalendarId: defaultCalendarId || user.googleCalendarId,
        syncPastEvents: syncPastEvents ?? false,
        syncReminders: syncReminders ?? false
      }
    });
    return;
  } catch (error: any) {
    secureLogger.error('[CalendarSync] Failed to update sync settings:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to update sync settings',
        details: error.message
      }
    });
    return;
  }
});

// @route   GET /api/v2/calendar/connections
// @desc    Get connected calendars
// @access  Private
router.get('/connections', protect, async (req, res) => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: { message: 'User not authenticated' }
      });
    }

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' }
      });
    }

    if (!user.googleCalendarTokens?.accessToken) {
      return res.json({ success: true, data: [] });
    }

    // Return a simple connected calendar response
    const calendars = [{
      id: 'primary',
      provider: 'google' as const,
      displayName: 'Primary Calendar',
      isConnected: true,
      email: user.email,
      lastSyncedAt: new Date().toISOString()
    }];

    res.json({
      success: true,
      data: calendars
    });
    return;
  } catch (error: any) {
    secureLogger.error('[CalendarSync] Failed to get connected calendars:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Failed to get connected calendars',
        details: error.message
      }
    });
    return;
  }
});

export default router;
