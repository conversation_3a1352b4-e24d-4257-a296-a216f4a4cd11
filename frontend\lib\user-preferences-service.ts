import axios from 'axios';
import { GroceryPreferences, UpdateGroceryPreferencesData } from './types/user.model';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api',
  withCredentials: true,
});

// Add request interceptor to include auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response types
interface ApiSuccessResponse<T> {
  success: true;
  data: T;
}

interface ApiErrorResponse {
  success: false;
  error: {
    message: string;
    code?: string;
  };
}

type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;

class UserPreferencesService {
  private endpoint = '/api/settings';

  /**
   * Get grocery preferences for the current user
   */
  async getGroceryPreferences(): Promise<GroceryPreferences> {
    try {
      const response = await api.get<ApiResponse<GroceryPreferences>>(`${this.endpoint}/grocery-preferences`);

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error.message || 'Failed to get grocery preferences');
      }
    } catch (error: any) {
      if (axios.isAxiosError(error) && error.response?.data?.error?.message) {
        throw new Error(error.response.data.error.message);
      }
      throw new Error('Failed to get grocery preferences');
    }
  }

  /**
   * Update grocery preferences for the current user
   */
  async updateGroceryPreferences(preferences: UpdateGroceryPreferencesData): Promise<GroceryPreferences> {
    try {
      const response = await api.put<ApiResponse<GroceryPreferences>>(`${this.endpoint}/grocery-preferences`, {
        groceryPreferences: preferences
      });

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error.message || 'Failed to update grocery preferences');
      }
    } catch (error: any) {
      if (axios.isAxiosError(error) && error.response?.data?.error?.message) {
        throw new Error(error.response.data.error.message);
      }
      throw new Error('Failed to update grocery preferences');
    }
  }

  /**
   * Set default list to personal
   */
  async setDefaultToPersonal(): Promise<GroceryPreferences> {
    return this.updateGroceryPreferences({
      defaultListType: 'personal',
      defaultSharedListOwnerId: null
    });
  }

  /**
   * Set default list to a shared list
   */
  async setDefaultToSharedList(listOwnerId: string): Promise<GroceryPreferences> {
    return this.updateGroceryPreferences({
      defaultListType: 'shared',
      defaultSharedListOwnerId: listOwnerId
    });
  }

  /**
   * Toggle auto switch to default list
   */
  async toggleAutoSwitchToDefault(enabled: boolean): Promise<GroceryPreferences> {
    return this.updateGroceryPreferences({
      autoSwitchToDefault: enabled
    });
  }

  /**
   * Toggle show personal list in sidebar
   */
  async toggleShowPersonalListInSidebar(show: boolean): Promise<GroceryPreferences> {
    return this.updateGroceryPreferences({
      showPersonalListInSidebar: show
    });
  }
}

// Export singleton instance
export const userPreferencesService = new UserPreferencesService();
export default userPreferencesService;
