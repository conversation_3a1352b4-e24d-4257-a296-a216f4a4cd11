import { Request, Response, NextFunction } from 'express';
import { User } from '../models/User';
import secureLogger from '../utils/secureLogger';

// Get grocery preferences for the authenticated user
export const getGroceryPreferences = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = req.user?.userId;

  if (!userId) {
    res.status(401).json({
      success: false,
      error: {
        message: 'User not authenticated',
        code: 'AUTHENTICATION_REQUIRED'
      }
    });
    return;
  }

  try {
    const user = await User.findById(userId).select('preferences.grocery');

    if (!user) {
      res.status(404).json({
        success: false,
        error: {
          message: 'User not found',
          code: 'USER_NOT_FOUND'
        }
      });
      return;
    }

    // Return grocery preferences with defaults if not set
    const groceryPreferences = {
      defaultListType: user.preferences?.grocery?.defaultListType || 'personal',
      defaultSharedListOwnerId: user.preferences?.grocery?.defaultSharedListOwnerId || null,
      autoSwitchToDefault: user.preferences?.grocery?.autoSwitchToDefault || false,
      showPersonalListInSidebar: user.preferences?.grocery?.showPersonalListInSidebar !== false // Default to true
    };

    res.status(200).json({
      success: true,
      data: groceryPreferences
    });

    secureLogger.log(`Retrieved grocery preferences for user ${userId}`);
  } catch (error) {
    secureLogger.error(`Error retrieving grocery preferences for user ${userId}:`, error);
    next(error);
  }
};

// Update grocery preferences for the authenticated user
export const updateGroceryPreferences = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = req.user?.userId;
  const { defaultListType, defaultSharedListOwnerId, autoSwitchToDefault, showPersonalListInSidebar } = req.body;

  if (!userId) {
    res.status(401).json({
      success: false,
      error: {
        message: 'User not authenticated',
        code: 'AUTHENTICATION_REQUIRED'
      }
    });
    return;
  }

  try {
    const user = await User.findById(userId);

    if (!user) {
      res.status(404).json({
        success: false,
        error: {
          message: 'User not found',
          code: 'USER_NOT_FOUND'
        }
      });
      return;
    }

    // Initialize preferences if they don't exist
    if (!user.preferences) {
      user.preferences = {};
    }
    if (!user.preferences.grocery) {
      user.preferences.grocery = {};
    }

    // Update only provided fields
    if (defaultListType !== undefined) {
      user.preferences.grocery.defaultListType = defaultListType;
    }
    if (defaultSharedListOwnerId !== undefined) {
      user.preferences.grocery.defaultSharedListOwnerId = defaultSharedListOwnerId;
    }
    if (autoSwitchToDefault !== undefined) {
      user.preferences.grocery.autoSwitchToDefault = autoSwitchToDefault;
    }
    if (showPersonalListInSidebar !== undefined) {
      user.preferences.grocery.showPersonalListInSidebar = showPersonalListInSidebar;
    }

    // Validation: If defaultListType is 'shared', ensure defaultSharedListOwnerId is provided
    if (user.preferences.grocery.defaultListType === 'shared' && !user.preferences.grocery.defaultSharedListOwnerId) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Default shared list owner ID is required when default list type is shared',
          code: 'VALIDATION_ERROR'
        }
      });
      return;
    }

    await user.save();

    // Return updated preferences
    const updatedPreferences = {
      defaultListType: user.preferences.grocery.defaultListType,
      defaultSharedListOwnerId: user.preferences.grocery.defaultSharedListOwnerId,
      autoSwitchToDefault: user.preferences.grocery.autoSwitchToDefault,
      showPersonalListInSidebar: user.preferences.grocery.showPersonalListInSidebar
    };

    res.status(200).json({
      success: true,
      data: updatedPreferences
    });

    secureLogger.log(`Updated grocery preferences for user ${userId}:`, updatedPreferences);
  } catch (error) {
    secureLogger.error(`Error updating grocery preferences for user ${userId}:`, error);
    next(error);
  }
};
