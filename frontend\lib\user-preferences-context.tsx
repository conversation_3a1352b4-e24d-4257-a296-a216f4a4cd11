'use client';

import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import { GroceryPreferences, UpdateGroceryPreferencesData } from './types/user.model';
import userPreferencesService from './user-preferences-service';
import { useToast } from '@/components/ui/use-toast';

interface UserPreferencesContextType {
  // State
  groceryPreferences: GroceryPreferences | null;
  isLoading: boolean;
  error: string | null;

  // Methods
  fetchGroceryPreferences: () => Promise<void>;
  updateGroceryPreferences: (preferences: UpdateGroceryPreferencesData) => Promise<void>;
  setDefaultToPersonal: () => Promise<void>;
  setDefaultToSharedList: (listOwnerId: string) => Promise<void>;
  toggleAutoSwitchToDefault: (enabled: boolean) => Promise<void>;
  toggleShowPersonalListInSidebar: (show: boolean) => Promise<void>;
}

const UserPreferencesContext = createContext<UserPreferencesContextType | undefined>(undefined);

interface UserPreferencesProviderProps {
  children: React.ReactNode;
}

export const UserPreferencesProvider: React.FC<UserPreferencesProviderProps> = ({ children }) => {
  const [groceryPreferences, setGroceryPreferences] = useState<GroceryPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Fetch grocery preferences
  const fetchGroceryPreferences = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const preferences = await userPreferencesService.getGroceryPreferences();
      setGroceryPreferences(preferences);
    } catch (err: any) {
      console.error('Failed to fetch grocery preferences:', err);
      const errorMessage = err.message || 'Failed to load preferences.';
      setError(errorMessage);
      // Don't show toast for initial load failures
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update grocery preferences
  const updateGroceryPreferences = useCallback(async (preferences: UpdateGroceryPreferencesData) => {
    setIsLoading(true);
    setError(null);
    try {
      const updatedPreferences = await userPreferencesService.updateGroceryPreferences(preferences);
      setGroceryPreferences(updatedPreferences);
      toast({
        title: 'Preferences Updated',
        description: 'Your grocery preferences have been saved successfully.',
      });
    } catch (err: any) {
      console.error('Failed to update grocery preferences:', err);
      const errorMessage = err.message || 'Failed to update preferences.';
      setError(errorMessage);
      toast({
        title: 'Update Failed',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Set default to personal list
  const setDefaultToPersonal = useCallback(async () => {
    await updateGroceryPreferences({
      defaultListType: 'personal',
      defaultSharedListOwnerId: null
    });
  }, [updateGroceryPreferences]);

  // Set default to shared list
  const setDefaultToSharedList = useCallback(async (listOwnerId: string) => {
    await updateGroceryPreferences({
      defaultListType: 'shared',
      defaultSharedListOwnerId: listOwnerId
    });
  }, [updateGroceryPreferences]);

  // Toggle auto switch to default
  const toggleAutoSwitchToDefault = useCallback(async (enabled: boolean) => {
    await updateGroceryPreferences({
      autoSwitchToDefault: enabled
    });
  }, [updateGroceryPreferences]);

  // Toggle show personal list in sidebar
  const toggleShowPersonalListInSidebar = useCallback(async (show: boolean) => {
    await updateGroceryPreferences({
      showPersonalListInSidebar: show
    });
  }, [updateGroceryPreferences]);

  // Load preferences on mount
  useEffect(() => {
    fetchGroceryPreferences();
  }, [fetchGroceryPreferences]);

  // Memoize context value
  const contextValue = useMemo(() => ({
    groceryPreferences,
    isLoading,
    error,
    fetchGroceryPreferences,
    updateGroceryPreferences,
    setDefaultToPersonal,
    setDefaultToSharedList,
    toggleAutoSwitchToDefault,
    toggleShowPersonalListInSidebar,
  }), [
    groceryPreferences,
    isLoading,
    error,
    fetchGroceryPreferences,
    updateGroceryPreferences,
    setDefaultToPersonal,
    setDefaultToSharedList,
    toggleAutoSwitchToDefault,
    toggleShowPersonalListInSidebar,
  ]);

  return (
    <UserPreferencesContext.Provider value={contextValue}>
      {children}
    </UserPreferencesContext.Provider>
  );
};

// Custom hook to use user preferences context
export const useUserPreferences = (): UserPreferencesContextType => {
  const context = useContext(UserPreferencesContext);
  if (context === undefined) {
    throw new Error('useUserPreferences must be used within a UserPreferencesProvider');
  }
  return context;
};
