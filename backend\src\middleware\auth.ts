import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import * as jwt from 'jsonwebtoken';
import { User } from '../models/User';
import secureLogger from '../utils/secureLogger';

// Type for JWT secret
type Secret = string | Buffer | { key: string | Buffer; passphrase: string };

// Extend Express Request type to include user information
declare global {
  namespace Express {
    interface Request {
      user?: {
        userId: string;
      };
    }
  }
}

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-for-development';

export const auth: RequestHandler = async (req, res, next): Promise<void> => {
  try {
    // Get token from header or cookie
    let token: string | undefined;

    // 1. Check for Bearer token in Authorization header (Mobile / API clients)
    const authHeader = req.header('Authorization');
    if (authHeader?.startsWith('Bearer ')) {
      token = authHeader.replace('Bearer ', '');
      secureLogger.log('Auth middleware: Found Bearer token.');
    }
    // 2. Fallback: Check for access token in cookie (Web client)
    else if (req.cookies?.access_token) {
      token = req.cookies.access_token;
      secureLogger.log('Auth middleware: Found access_token cookie.');
    }

    // If no token found by either method
    if (!token) {
      secureLogger.log('Auth middleware: No token found.');
      res.status(401).json({
        success: false,
        error: {
          message: 'No token provided',
          code: 'NO_TOKEN'
        }
      });
      return;
    }

    try {
      // Verify token
      secureLogger.log('Auth middleware: Verifying token...');
      const decoded = jwt.verify(token, JWT_SECRET as Secret) as { userId: string };
      secureLogger.log('Auth middleware: Token verified successfully', { userId: decoded.userId });

      // Add user info to request
      req.user = {
        userId: decoded.userId
      };

      secureLogger.log('Auth middleware: Proceeding to next middleware', { path: req.originalUrl });
      next();
    } catch (error: any) {
      // Token is invalid or expired
      secureLogger.error('Auth middleware: Token verification failed', { path: req.originalUrl, error });

      // Handle specific JWT errors
      if (error.name === 'TokenExpiredError') {
        res.status(401).json({
          success: false,
          error: {
            message: 'Access token expired',
            code: 'TOKEN_EXPIRED'
          }
        });
      } else if (error.name === 'JsonWebTokenError') {
        res.status(401).json({
          success: false,
          error: {
            message: 'Invalid access token',
            code: 'TOKEN_INVALID'
          }
        });
      } else {
        // Generic internal error for other verification issues
        res.status(500).json({
          success: false,
          error: {
            message: 'Token verification failed',
            code: 'TOKEN_VERIFICATION_ERROR'
          }
        });
      }
      return; // Stop execution
    }
  } catch (error) {
    secureLogger.error('Auth middleware error:', error);
    res.status(500).json({
      success: false,
      error: {
        message: 'Server error'
      }
    });
    return; // Stop execution
  }
};

// Export auth as protect for backward compatibility
export const protect = auth;
