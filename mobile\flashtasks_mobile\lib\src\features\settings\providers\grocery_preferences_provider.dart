import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/services/grocery_preferences_service.dart';
import '../../../core/providers/network_provider.dart';
import '../models/grocery_preferences.dart';

/// State class for grocery preferences
class GroceryPreferencesState {
  final GroceryPreferences? preferences;
  final bool isLoading;
  final String? error;

  const GroceryPreferencesState({
    this.preferences,
    this.isLoading = false,
    this.error,
  });

  GroceryPreferencesState copyWith({
    GroceryPreferences? preferences,
    bool? isLoading,
    String? error,
    bool clearError = false,
  }) {
    return GroceryPreferencesState(
      preferences: preferences ?? this.preferences,
      isLoading: isLoading ?? this.isLoading,
      error: clearError ? null : (error ?? this.error),
    );
  }
}

/// Notifier for grocery preferences
class GroceryPreferencesNotifier extends StateNotifier<GroceryPreferencesState> {
  final GroceryPreferencesService _service;

  GroceryPreferencesNotifier({required GroceryPreferencesService service})
      : _service = service,
        super(const GroceryPreferencesState()) {
    // Load preferences on initialization
    fetchGroceryPreferences();
  }

  /// Fetch grocery preferences from the API
  Future<void> fetchGroceryPreferences() async {
    state = state.copyWith(isLoading: true, clearError: true);

    try {
      final preferences = await _service.getGroceryPreferences();
      state = state.copyWith(
        preferences: preferences,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Update grocery preferences
  Future<void> updateGroceryPreferences(UpdateGroceryPreferencesData preferences) async {
    state = state.copyWith(isLoading: true, clearError: true);

    try {
      final updatedPreferences = await _service.updateGroceryPreferences(preferences);
      state = state.copyWith(
        preferences: updatedPreferences,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// Set default to personal list
  Future<void> setDefaultToPersonal() async {
    state = state.copyWith(isLoading: true, clearError: true);

    try {
      final updatedPreferences = await _service.setDefaultToPersonal();
      state = state.copyWith(
        preferences: updatedPreferences,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// Set default to shared list
  Future<void> setDefaultToSharedList(String listOwnerId) async {
    state = state.copyWith(isLoading: true, clearError: true);

    try {
      final updatedPreferences = await _service.setDefaultToSharedList(listOwnerId);
      state = state.copyWith(
        preferences: updatedPreferences,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// Toggle auto switch to default
  Future<void> toggleAutoSwitchToDefault(bool enabled) async {
    state = state.copyWith(isLoading: true, clearError: true);

    try {
      final updatedPreferences = await _service.toggleAutoSwitchToDefault(enabled);
      state = state.copyWith(
        preferences: updatedPreferences,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// Toggle show personal list in sidebar
  Future<void> toggleShowPersonalListInSidebar(bool show) async {
    state = state.copyWith(isLoading: true, clearError: true);

    try {
      final updatedPreferences = await _service.toggleShowPersonalListInSidebar(show);
      state = state.copyWith(
        preferences: updatedPreferences,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// Refresh preferences
  Future<void> refreshPreferences() async {
    await fetchGroceryPreferences();
  }
}

/// Provider for grocery preferences service
final groceryPreferencesServiceProvider = Provider<GroceryPreferencesService>((ref) {
  final apiClient = ref.watch(dioProvider);
  return GroceryPreferencesService(apiClient: apiClient);
});

/// Provider for grocery preferences state
final groceryPreferencesProvider = StateNotifierProvider<GroceryPreferencesNotifier, GroceryPreferencesState>((ref) {
  final service = ref.watch(groceryPreferencesServiceProvider);
  return GroceryPreferencesNotifier(service: service);
});
