'use client';

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar as CalendarIcon, Clock, X, Loader2 } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format, addHours, isBefore } from 'date-fns';
import { cn } from '@/lib/utils';
import { TaskPriority } from '@/lib/types/task.model';

export interface TaskFormData {
  id?: string;
  title: string;
  description: string;
  start: Date;
  end: Date;
  allDay: boolean;
  priority: TaskPriority;
}

interface TaskFormProps {
  initialData: TaskFormData;
  onSubmit: (data: TaskFormData) => void;
  onDelete?: () => void;
  onCancel: () => void;
  isLoading?: boolean;
}

export function TaskForm({
  initialData,
  onSubmit,
  onDelete,
  onCancel,
  isLoading = false,
}: TaskFormProps) {
  const [formData, setFormData] = useState<TaskFormData>(() => ({
    ...initialData,
    // Ensure dates are Date objects
    start: initialData.start ? new Date(initialData.start) : new Date(),
    end: initialData.end ? new Date(initialData.end) : addHours(new Date(), 1),
  }));
  const [showStartCalendar, setShowStartCalendar] = useState(false);
  const [showEndCalendar, setShowEndCalendar] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.title.trim()) {
      return;
    }
    
    // Ensure end date is after start date
    if (isBefore(formData.end, formData.start)) {
      // Swap if end is before start
      const temp = formData.start;
      setFormData(prev => ({
        ...prev,
        start: formData.end,
        end: temp
      }));
    }
    
    try {
      setIsSubmitting(true);
      await onSubmit(formData);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid gap-2">
        <label htmlFor="title" className="text-sm font-medium">
          Title <span className="text-red-500">*</span>
        </label>
        <Input
          id="title"
          value={formData.title}
          onChange={(e) => setFormData({...formData, title: e.target.value})}
          placeholder="Task title"
          required
        />
      </div>
      
      <div className="grid gap-2">
        <label htmlFor="description" className="text-sm font-medium">
          Description
        </label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({...formData, description: e.target.value})}
          placeholder="Add details about your task"
          rows={3}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="grid gap-2">
          <label className="text-sm font-medium">Start</label>
          <Popover open={showStartCalendar} onOpenChange={setShowStartCalendar}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  'w-full justify-start text-left font-normal',
                  !formData.start && 'text-muted-foreground'
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.start ? (
                  formData.allDay ? (
                    format(formData.start, 'PPP')
                  ) : (
                    format(formData.start, 'PPPp')
                  )
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.start}
                onSelect={(date) => {
                  if (date) {
                    const newStart = new Date(formData.start);
                    newStart.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());
                    setFormData({...formData, start: newStart});
                  }
                }}
                initialFocus
              />
              {!formData.allDay && (
                <div className="p-3 border-t">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <Input
                      type="time"
                      value={format(formData.start, 'HH:mm')}
                      onChange={(e) => {
                        const [hours, minutes] = e.target.value.split(':').map(Number);
                        const newStart = new Date(formData.start);
                        newStart.setHours(hours, minutes);
                        setFormData({...formData, start: newStart});
                      }}
                      className="w-full"
                    />
                  </div>
                </div>
              )}
            </PopoverContent>
          </Popover>
        </div>

        <div className="grid gap-2">
          <label className="text-sm font-medium">End</label>
          <Popover open={showEndCalendar} onOpenChange={setShowEndCalendar}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  'w-full justify-start text-left font-normal',
                  !formData.end && 'text-muted-foreground'
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.end ? (
                  formData.allDay ? (
                    format(formData.end, 'PPP')
                  ) : (
                    format(formData.end, 'PPPp')
                  )
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.end}
                onSelect={(date) => {
                  if (date) {
                    const newEnd = new Date(formData.end);
                    newEnd.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());
                    setFormData({...formData, end: newEnd});
                  }
                }}
                initialFocus
              />
              {!formData.allDay && (
                <div className="p-3 border-t">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <Input
                      type="time"
                      value={format(formData.end, 'HH:mm')}
                      onChange={(e) => {
                        const [hours, minutes] = e.target.value.split(':').map(Number);
                        const newEnd = new Date(formData.end);
                        newEnd.setHours(hours, minutes);
                        setFormData({...formData, end: newEnd});
                      }}
                      className="w-full"
                    />
                  </div>
                </div>
              )}
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="allDay"
            checked={formData.allDay}
            onChange={(e) => {
              const allDay = e.target.checked;
              const start = new Date(formData.start);
              const end = new Date(formData.end);
              
              if (allDay) {
                start.setHours(0, 0, 0, 0);
                end.setHours(23, 59, 59, 999);
              } else {
                start.setHours(12, 0, 0, 0);
                end.setHours(13, 0, 0, 0);
              }
              
              setFormData({
                ...formData,
                allDay,
                start,
                end,
              });
            }}
            className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <label htmlFor="allDay" className="text-sm font-medium">
            <Button
              variant="outline"
              className={cn(
                'w-full justify-start text-left font-normal',
                !formData.start && 'text-muted-foreground'
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {formData.start ? (
                formData.allDay ? (
                  format(formData.start, 'PPP')
                ) : (
                  format(formData.start, 'PPPp')
                )
              ) : (
                <span>Pick a date</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={formData.start}
              onSelect={(date) => {
                if (date) {
                  const newStart = new Date(formData.start);
                  newStart.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());
                  setFormData({...formData, start: newStart});
                }
              }}
              initialFocus
            />
            {!formData.allDay && (
              <div className="p-3 border-t">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <Input
                    type="time"
                    value={format(formData.start, 'HH:mm')}
                    onChange={(e) => {
                      const [hours, minutes] = e.target.value.split(':').map(Number);
                      const newStart = new Date(formData.start);
                      newStart.setHours(hours, minutes);
                      setFormData({...formData, start: newStart});
                    }}
                    className="w-full"
                  />
                </div>
              </div>
            )}
          </PopoverContent>
        </Popover>
      </div>

      <div className="grid gap-2">
        <label className="text-sm font-medium">End</label>
        <Popover open={showEndCalendar} onOpenChange={setShowEndCalendar}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-full justify-start text-left font-normal',
                !formData.end && 'text-muted-foreground'
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {formData.end ? (
                formData.allDay ? (
                  format(formData.end, 'PPP')
                ) : (
                  format(formData.end, 'PPPp')
                )
              ) : (
                <span>Pick a date</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={formData.end}
              onSelect={(date) => {
                if (date) {
                  const newEnd = new Date(formData.end);
                  newEnd.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());
                  setFormData({...formData, end: newEnd});
                }
              }}
              initialFocus
            />
            {!formData.allDay && (
              <div className="p-3 border-t">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <Input
                    type="time"
                    value={format(formData.end, 'HH:mm')}
                    onChange={(e) => {
                      const [hours, minutes] = e.target.value.split(':').map(Number);
                      const newEnd = new Date(formData.end);
                      newEnd.setHours(hours, minutes);
                      setFormData({...formData, end: newEnd});
                    }}
                    className="w-full"
                  />
                </div>
              </div>
            )}
          </PopoverContent>
        </Popover>
      </div>
    </div>

    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="allDay"
          checked={formData.allDay}
          onChange={(e) => {
            const allDay = e.target.checked;
            const start = new Date(formData.start);
            const end = new Date(formData.end);
            
            if (allDay) {
              start.setHours(0, 0, 0, 0);
              end.setHours(23, 59, 59, 999);
            } else {
              start.setHours(12, 0, 0, 0);
              end.setHours(13, 0, 0, 0);
            }
            
            setFormData({
              ...formData,
              allDay,
              start,
              end,
            });
          }}
          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor="allDay" className="text-sm font-medium">
          All day
        </label>
      </div>

      <div className="flex-1 max-w-[180px]">
        <select
          id="priority"
          value={formData.priority}
          onChange={(e) => setFormData({...formData, priority: e.target.value as TaskPriority})}
          className="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
        >
          <option value="Low">Low Priority</option>
          <option value="Medium">Medium Priority</option>
          <option value="High">High Priority</option>
          <option value="Critical">Critical</option>
        </select>
      </div>
    </div>

    <div className="flex justify-between pt-4">
      <div>
        {onDelete && (
          <Button
            type="button"
            variant="destructive"
            onClick={onDelete}
            disabled={isLoading || isSubmitting}
            className="min-w-[80px]"
          >
            {isSubmitting ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : 'Delete'}
          </Button>
        )}
      </div>
      <div className="space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading || isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={!formData.title.trim() || isLoading || isSubmitting}
          className="min-w-[80px]"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : 'Save'}
        </Button>
      </div>
    </div>
  </form>
);
}
