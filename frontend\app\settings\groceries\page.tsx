"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { useUserPreferences } from '@/lib/user-preferences-context';
import { useGrocery } from '@/lib/grocery-context';
import { AlertCircle, CheckCircle2 } from 'lucide-react';

export default function GroceryPreferencesPage() {
  const { toast } = useToast();
  const {
    groceryPreferences,
    isLoading: preferencesLoading,
    error: preferencesError,
    updateGroceryPreferences,
    setDefaultToPersonal,
    setDefaultToSharedList,
    toggleAutoSwitchToDefault,
    toggleShowPersonalListInSidebar,
  } = useUserPreferences();

  const {
    sharedLists,
    isLoading: sharedListsLoading,
    fetchSharedLists,
  } = useGrocery();

  const [isSaving, setIsSaving] = useState(false);

  // Load shared lists on mount
  useEffect(() => {
    fetchSharedLists();
  }, [fetchSharedLists]);

  const handleDefaultListTypeChange = async (value: string) => {
    setIsSaving(true);
    try {
      if (value === 'personal') {
        await setDefaultToPersonal();
      } else if (value === 'shared') {
        // If switching to shared but no shared list selected, show error
        if (!groceryPreferences?.defaultSharedListOwnerId && sharedLists.length > 0) {
          await setDefaultToSharedList(sharedLists[0].userId._id);
        } else if (sharedLists.length === 0) {
          toast({
            title: 'No Shared Lists',
            description: 'You need to have access to at least one shared list to set it as default.',
            variant: 'destructive',
          });
          return;
        }
      }
    } catch (error) {
      // Error handling is done in the context
    } finally {
      setIsSaving(false);
    }
  };

  const handleSharedListChange = async (listOwnerId: string) => {
    setIsSaving(true);
    try {
      await setDefaultToSharedList(listOwnerId);
    } catch (error) {
      // Error handling is done in the context
    } finally {
      setIsSaving(false);
    }
  };

  const handleAutoSwitchToggle = async (enabled: boolean) => {
    setIsSaving(true);
    try {
      await toggleAutoSwitchToDefault(enabled);
    } catch (error) {
      // Error handling is done in the context
    } finally {
      setIsSaving(false);
    }
  };

  const handleShowPersonalToggle = async (show: boolean) => {
    setIsSaving(true);
    try {
      await toggleShowPersonalListInSidebar(show);
    } catch (error) {
      // Error handling is done in the context
    } finally {
      setIsSaving(false);
    }
  };

  if (preferencesLoading || sharedListsLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-96" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-6 w-32" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (preferencesError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-destructive">
            <AlertCircle className="mr-2 h-5 w-5" />
            Error Loading Preferences
          </CardTitle>
          <CardDescription>
            {preferencesError}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => window.location.reload()}>
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  const currentDefaultList = groceryPreferences?.defaultListType || 'personal';
  const currentSharedListId = groceryPreferences?.defaultSharedListOwnerId;
  const autoSwitch = groceryPreferences?.autoSwitchToDefault || false;
  const showPersonal = groceryPreferences?.showPersonalListInSidebar !== false;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Default Grocery List</CardTitle>
          <CardDescription>
            Choose which grocery list to use by default when adding items
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="default-list-type">Default List Type</Label>
              <Select
                value={currentDefaultList}
                onValueChange={handleDefaultListTypeChange}
                disabled={isSaving}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select default list type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="personal">My Personal List</SelectItem>
                  <SelectItem value="shared" disabled={sharedLists.length === 0}>
                    Shared List {sharedLists.length === 0 && '(No shared lists available)'}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {currentDefaultList === 'shared' && sharedLists.length > 0 && (
              <div className="space-y-2">
                <Label htmlFor="shared-list">Select Shared List</Label>
                <Select
                  value={currentSharedListId || ''}
                  onValueChange={handleSharedListChange}
                  disabled={isSaving}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a shared list" />
                  </SelectTrigger>
                  <SelectContent>
                    {sharedLists.map((list) => (
                      <SelectItem key={list.userId._id} value={list.userId._id}>
                        {list.userId.name}'s List ({list.role})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Auto-switch to default list</Label>
              <p className="text-sm text-muted-foreground">
                Automatically switch to your default list when opening the grocery page
              </p>
            </div>
            <Switch
              checked={autoSwitch}
              onCheckedChange={handleAutoSwitchToggle}
              disabled={isSaving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Show personal list in sidebar</Label>
              <p className="text-sm text-muted-foreground">
                Display your personal grocery list in the navigation sidebar
              </p>
            </div>
            <Switch
              checked={showPersonal}
              onCheckedChange={handleShowPersonalToggle}
              disabled={isSaving}
            />
          </div>

          {isSaving && (
            <div className="flex items-center text-sm text-muted-foreground">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
              Saving preferences...
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle2 className="mr-2 h-5 w-5 text-green-600" />
            Quick Add & AI Suggestions
          </CardTitle>
          <CardDescription>
            Items added via quick add and AI suggestions will automatically go to your active list
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            When you use the quick add feature or accept AI suggestions, items will be added to whichever 
            grocery list you're currently viewing. This ensures items go to the right place based on your context.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
