import { Request, Response, NextFunction } from 'express';
import mongoose, { Types } from 'mongoose';
import { SharedGroceryList } from '../models/SharedGroceryList';
import { GroceryItem } from '../models/GroceryItem';
import { User } from '../models/User';
import { socketService } from '../services/socketService';
import secureLogger from '../utils/secureLogger';

const sharedGroceryListController = {
  // Get all shared grocery lists for the user
  getSharedLists: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      const lists = await SharedGroceryList.find({
        $or: [
          { owner: userId },
          { 'members.userId': userId }
        ],
        isActive: true
      })
      .populate('owner', 'name email')
      .populate('members.userId', 'name email')
      .populate('members.invitedBy', 'name email')
      .sort({ lastActivityAt: -1 });

      // Get item counts for each list
      const listsWithCounts = await Promise.all(
        lists.map(async (list) => {
          const totalItems = await GroceryItem.countDocuments({ sharedListId: list._id });
          const checkedItems = await GroceryItem.countDocuments({
            sharedListId: list._id,
            isChecked: true
          });

          return {
            ...list.toObject(),
            itemCounts: {
              total: totalItems,
              checked: checkedItems,
              unchecked: totalItems - checkedItems
            }
          };
        })
      );

      res.status(200).json({
        success: true,
        data: listsWithCounts
      });
    } catch (error) {
      secureLogger.error(`Error fetching shared grocery lists for user ${userId}:`, error);
      next(error);
    }
  },

  // Create a new shared grocery list
  createSharedList: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      const { name, description, settings } = req.body;

      if (!name || typeof name !== 'string' || name.trim() === '') {
        res.status(400).json({
          success: false,
          error: {
            message: 'List name is required',
            code: 'VALIDATION_ERROR'
          }
        });
        return;
      }

      const newList = new SharedGroceryList({
        name: name.trim(),
        description: description?.trim(),
        owner: new mongoose.Types.ObjectId(userId),
        members: [], // Owner is not included in members array
        settings: {
          allowMemberInvites: settings?.allowMemberInvites ?? false,
          requireApprovalForEdits: settings?.requireApprovalForEdits ?? false,
          notifyOnChanges: settings?.notifyOnChanges ?? true,
        }
      });

      const savedList = await newList.save();

      // Populate owner information
      await savedList.populate('owner', 'name email');

      res.status(201).json({
        success: true,
        data: {
          ...savedList.toObject(),
          itemCounts: {
            total: 0,
            checked: 0,
            unchecked: 0
          }
        }
      });

      secureLogger.log(`User ${userId} created shared grocery list: ${savedList._id}`);
    } catch (error) {
      secureLogger.error(`Error creating shared grocery list for user ${userId}:`, error);
      next(error);
    }
  },

  // Get a specific shared grocery list with its items
  getSharedList: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const { listId } = req.params;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    if (!mongoose.Types.ObjectId.isValid(listId)) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Invalid list ID format',
          code: 'INVALID_ID_FORMAT'
        }
      });
      return;
    }

    try {
      const list = await SharedGroceryList.findById(listId)
        .populate('owner', 'name email')
        .populate('members.userId', 'name email')
        .populate('members.invitedBy', 'name email');

      if (!list) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Shared grocery list not found',
            code: 'RESOURCE_NOT_FOUND'
          }
        });
        return;
      }

      // Check if user has access to this list
      if (!list.canView(new mongoose.Types.ObjectId(userId))) {
        res.status(403).json({
          success: false,
          error: {
            message: 'Access denied to this grocery list',
            code: 'FORBIDDEN_OPERATION'
          }
        });
        return;
      }

      // Get grocery items for this list
      const items = await GroceryItem.find({ sharedListId: listId })
        .populate('addedBy', 'name email')
        .populate('checkedBy', 'name email')
        .populate('lastModifiedBy', 'name email')
        .sort({ isChecked: 1, createdAt: -1 });

      // Get item counts
      const totalItems = items.length;
      const checkedItems = items.filter(item => item.isChecked).length;

      res.status(200).json({
        success: true,
        data: {
          list: list.toObject(),
          items,
          itemCounts: {
            total: totalItems,
            checked: checkedItems,
            unchecked: totalItems - checkedItems
          }
        }
      });
    } catch (error) {
      secureLogger.error(`Error fetching shared grocery list ${listId} for user ${userId}:`, error);
      next(error);
    }
  },

  // Update shared grocery list settings
  updateSharedList: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const { listId } = req.params;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    if (!mongoose.Types.ObjectId.isValid(listId)) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Invalid list ID format',
          code: 'INVALID_ID_FORMAT'
        }
      });
      return;
    }

    try {
      const list = await SharedGroceryList.findById(listId);

      if (!list) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Shared grocery list not found',
            code: 'RESOURCE_NOT_FOUND'
          }
        });
        return;
      }

      // Only owner can update list settings
      if (list.owner.toString() !== userId) {
        res.status(403).json({
          success: false,
          error: {
            message: 'Only the list owner can update settings',
            code: 'FORBIDDEN_OPERATION'
          }
        });
        return;
      }

      const { name, description, settings } = req.body;

      // Update fields if provided
      if (name !== undefined) {
        if (!name || typeof name !== 'string' || name.trim() === '') {
          res.status(400).json({
            success: false,
            error: {
              message: 'List name cannot be empty',
              code: 'VALIDATION_ERROR'
            }
          });
          return;
        }
        list.name = name.trim();
      }

      if (description !== undefined) {
        list.description = description?.trim();
      }

      if (settings) {
        if (settings.allowMemberInvites !== undefined) {
          list.settings.allowMemberInvites = Boolean(settings.allowMemberInvites);
        }
        if (settings.requireApprovalForEdits !== undefined) {
          list.settings.requireApprovalForEdits = Boolean(settings.requireApprovalForEdits);
        }
        if (settings.notifyOnChanges !== undefined) {
          list.settings.notifyOnChanges = Boolean(settings.notifyOnChanges);
        }
      }

      list.lastActivityAt = new Date();
      const updatedList = await list.save();

      // Populate owner information
      await updatedList.populate('owner', 'name email');
      await updatedList.populate('members.userId', 'name email');

      res.status(200).json({
        success: true,
        data: updatedList
      });

      // Emit real-time update to all list members
      socketService.emitSharedListUpdated(
        listId,
        updatedList.toObject(),
        new mongoose.Types.ObjectId(userId)
      );

      secureLogger.log(`User ${userId} updated shared grocery list: ${listId}`);
    } catch (error) {
      secureLogger.error(`Error updating shared grocery list ${listId} for user ${userId}:`, error);
      next(error);
    }
  },

  // Add member to shared grocery list
  addMember: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const { listId } = req.params;
    const { email, role = 'viewer' } = req.body;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    if (!mongoose.Types.ObjectId.isValid(listId)) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Invalid list ID format',
          code: 'INVALID_ID_FORMAT'
        }
      });
      return;
    }

    if (!email || !['editor', 'viewer'].includes(role)) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Valid email and role (editor/viewer) are required',
          code: 'VALIDATION_ERROR'
        }
      });
      return;
    }

    try {
      const list = await SharedGroceryList.findById(listId);

      if (!list) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Shared grocery list not found',
            code: 'RESOURCE_NOT_FOUND'
          }
        });
        return;
      }

      // Check if user can add members (owner or member with permission)
      const userRole = list.getMemberRole(new mongoose.Types.ObjectId(userId));
      if (userRole !== 'owner' && !(userRole && list.settings.allowMemberInvites)) {
        res.status(403).json({
          success: false,
          error: {
            message: 'You do not have permission to add members',
            code: 'FORBIDDEN_OPERATION'
          }
        });
        return;
      }

      // Find user by email
      const userToAdd = await User.findOne({ email: email.toLowerCase().trim() });
      if (!userToAdd) {
        res.status(404).json({
          success: false,
          error: {
            message: 'User with this email not found',
            code: 'RESOURCE_NOT_FOUND'
          }
        });
        return;
      }

      // Check if user is already a member or owner
      if (list.owner.toString() === (userToAdd._id as mongoose.Types.ObjectId).toString()) {
        res.status(400).json({
          success: false,
          error: {
            message: 'User is already the owner of this list',
            code: 'VALIDATION_ERROR'
          }
        });
        return;
      }

      if (list.isMember(userToAdd._id as mongoose.Types.ObjectId)) {
        res.status(400).json({
          success: false,
          error: {
            message: 'User is already a member of this list',
            code: 'VALIDATION_ERROR'
          }
        });
        return;
      }

      // Add member to the list
      await list.addMember(userToAdd._id as mongoose.Types.ObjectId, role, new mongoose.Types.ObjectId(userId));

      // Populate the updated list
      await list.populate('owner', 'name email');
      await list.populate('members.userId', 'name email');
      await list.populate('members.invitedBy', 'name email');

      res.status(200).json({
        success: true,
        data: {
          list: list.toObject(),
          addedMember: {
            userId: userToAdd._id,
            name: userToAdd.name,
            email: userToAdd.email,
            role
          }
        }
      });

      // Emit real-time update to all list members
      socketService.emitMemberAdded(
        listId,
        {
          userId: userToAdd._id,
          name: userToAdd.name,
          email: userToAdd.email,
          role
        },
        new mongoose.Types.ObjectId(userId)
      );

      secureLogger.log(`User ${userId} added member ${userToAdd._id} to shared grocery list: ${listId}`);
    } catch (error) {
      secureLogger.error(`Error adding member to shared grocery list ${listId}:`, error);
      next(error);
    }
  },

  // Remove member from shared grocery list
  removeMember: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const { listId, memberId } = req.params;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    if (!mongoose.Types.ObjectId.isValid(listId) || !mongoose.Types.ObjectId.isValid(memberId)) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Invalid ID format',
          code: 'INVALID_ID_FORMAT'
        }
      });
      return;
    }

    try {
      const list = await SharedGroceryList.findById(listId);

      if (!list) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Shared grocery list not found',
            code: 'RESOURCE_NOT_FOUND'
          }
        });
        return;
      }

      // Check permissions: owner can remove anyone, members can only remove themselves
      const userRole = list.getMemberRole(new mongoose.Types.ObjectId(userId));
      if (userRole !== 'owner' && userId !== memberId) {
        res.status(403).json({
          success: false,
          error: {
            message: 'You can only remove yourself from the list',
            code: 'FORBIDDEN_OPERATION'
          }
        });
        return;
      }

      // Remove member from the list
      await list.removeMember(new mongoose.Types.ObjectId(memberId));

      // Populate the updated list
      await list.populate('owner', 'name email');
      await list.populate('members.userId', 'name email');

      res.status(200).json({
        success: true,
        data: list.toObject()
      });

      // Emit real-time update to all list members
      socketService.emitMemberRemoved(
        listId,
        new mongoose.Types.ObjectId(memberId),
        new mongoose.Types.ObjectId(userId)
      );

      secureLogger.log(`User ${userId} removed member ${memberId} from shared grocery list: ${listId}`);
    } catch (error) {
      secureLogger.error(`Error removing member from shared grocery list ${listId}:`, error);
      next(error);
    }
  }
};

export default sharedGroceryListController;
