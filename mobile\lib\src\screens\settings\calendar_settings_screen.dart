import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../services/calendar_service.dart';
import '../../providers/auth_provider.dart';

class CalendarSettingsScreen extends ConsumerStatefulWidget {
  const CalendarSettingsScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<CalendarSettingsScreen> createState() => _CalendarSettingsScreenState();
}

class _CalendarSettingsScreenState extends ConsumerState<CalendarSettingsScreen> {
  bool _loading = false;

  Future<void> _handleConnect() async {
    setState(() => _loading = true);
    try {
      final calendarService = ref.read(calendarServiceProvider);
      final authUrl = await calendarService.getAuthUrl();
      if (await canLaunch(authUrl)) {
        await launch(authUrl);
      } else {
        throw Exception('Could not launch $authUrl');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to connect: $e')),
      );
    } finally {
      setState(() => _loading = false);
    }
  }

  Future<void> _handleDisconnect() async {
    setState(() => _loading = true);
    try {
      final calendarService = ref.read(calendarServiceProvider);
      await calendarService.disconnect();
      // Refresh user data to update calendar sync status
      await ref.read(authProvider.notifier).refreshUser();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Successfully disconnected from Google Calendar')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to disconnect: $e')),
      );
    } finally {
      setState(() => _loading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(authProvider);
    final calendarEnabled = user?.googleCalendarSyncEnabled ?? false;
    final lastSync = user?.lastGoogleCalendarSync;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Calendar Settings'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Google Calendar',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    calendarEnabled
                        ? 'Your tasks with deadlines will sync to Google Calendar'
                        : 'Connect your Google Calendar to sync tasks with deadlines',
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (_loading)
                    const Center(child: CircularProgressIndicator())
                  else
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: calendarEnabled ? _handleDisconnect : _handleConnect,
                        style: ElevatedButton.styleFrom(
                          primary: calendarEnabled ? Colors.red : null,
                        ),
                        child: Text(calendarEnabled ? 'Disconnect' : 'Connect'),
                      ),
                    ),
                  if (calendarEnabled && lastSync != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Last synced: ${lastSync.toLocal()}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
