import mongoose, { Document, Schema } from 'mongoose';
import * as bcrypt from 'bcryptjs';
import * as crypto from 'crypto';
import secureLogger from '../utils/secureLogger';

// Define interface for push token subdocument
interface IPushToken {
  token: string;
  deviceType: 'android' | 'ios' | 'web'; // Platform identifier
  createdAt: Date;
}

// Interface for User document
export interface IUser extends Document {
  name: string;
  email: string;
  password: string;
  roles: string[];
  emailVerified: boolean;
  emailVerificationToken?: string;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  avatar?: string;
  googleId?: string; // Google ID for Google Sign-In
  refreshTokens?: Array<{
    token: string;
    expires: Date;
    userAgent?: string;
    ipAddress?: string;
  }>;
  preferences?: {
    theme?: string;
    language?: string;
    timezone?: string; // User's timezone (e.g., 'America/New_York', 'UTC')
    notifications?: boolean;
    calendar?: {
      defaultView?: 'day' | 'week' | 'month' | 'agenda';
      weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6; // 0 = Sunday, 1 = Monday, etc.
      showWeekends?: boolean;
      timeFormat?: '12' | '24';
      defaultDuration?: number; // in minutes
      // Calendar sync settings
      syncEnabled?: boolean;
      syncReminders?: boolean;
      syncPastEvents?: boolean;
      defaultCalendarId?: string; // ID of the default calendar to use
      lastSyncedAt?: Date; // Last sync timestamp
    };
    grocery?: {
      defaultListType?: 'personal' | 'shared';
      defaultSharedListOwnerId?: string;
      autoSwitchToDefault?: boolean;
      showPersonalListInSidebar?: boolean;
    };
    [key: string]: any;
  };

  pushTokens?: IPushToken[]; // New field for device tokens
  age?: number;
  familyMembers?: Array<{
    name: string;
    relationship: string;
    age?: number;
  }>;
  workDetails?: {
    location: string;
    workingHours: {
      start: string;
      end: string;
    };
  };
  // Calendar integration settings
  calendarSettings?: {
    syncEnabled: boolean;
    defaultCalendarId?: string;
    syncPastEvents: boolean;
    syncReminders: boolean;
    lastSyncedAt?: Date;
  };
  
  // Google Calendar OAuth tokens
  googleCalendarTokens?: {
    accessToken: string;
    refreshToken?: string;
    expiryDate: number; // Timestamp in milliseconds
    scope: string;
    email?: string;
    name?: string;
    picture?: string;
  };
  
  // Calendar provider information
  calendarProvider?: 'google' | 'outlook' | 'apple' | 'other';
  
  // Deprecated fields (kept for backward compatibility)
  googleCalendarSyncEnabled?: boolean;
  googleCalendarId?: string;
  lastGoogleCalendarSync?: Date;
  homeLocation?: string;
  frequentLocations?: string[];
  purposes: string[];
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
  createPasswordResetToken(): string;
  createEmailVerificationToken(): string;
  createRefreshToken(userAgent?: string, ipAddress?: string): string;
  removeRefreshToken(token: string): Promise<void>;

  addPushToken(token: string, deviceType: 'android' | 'ios' | 'web'): Promise<void>; // New method
  removePushToken(token: string): Promise<void>; // New method
}

// Define schema for push token subdocument
const pushTokenSchema = new Schema<IPushToken>({
  token: { type: String, required: true },
  deviceType: { type: String, enum: ['android', 'ios', 'web'], required: true },
  createdAt: { type: Date, default: Date.now }
}, { _id: false }); // No separate _id needed for subdocs here

// User Schema
const userSchema = new Schema<IUser>(
  {
    // Calendar settings
    calendarSettings: {
      syncEnabled: { type: Boolean, default: true },
      defaultCalendarId: String,
      syncPastEvents: { type: Boolean, default: false },
      syncReminders: { type: Boolean, default: true },
      lastSyncedAt: Date
    },
    calendarProvider: {
      type: String,
      enum: ['google', 'outlook', 'apple', 'other'],
      default: 'google'
    },
    name: {
      type: String,
      required: [true, 'Name is required'],
      trim: true,
    },
    email: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      trim: true,
      lowercase: true,
      match: [/^\S+@\S+\.\S+$/, 'Please enter a valid email'],
    },
    password: {
      type: String,
      required: [true, 'Password is required'],
      minlength: [8, 'Password must be at least 8 characters long'],
      select: false, // Don't return password by default
    },
    roles: {
      type: [String],
      enum: ['user', 'admin'],
      default: ['user'],
    },
    avatar: {
      type: String,
      default: null,
    },
    googleId: {
      type: String,
      sparse: true, // Allow multiple null values (for users without Google Sign-In)
      index: true, // Index for faster lookups
    },
    emailVerified: {
      type: Boolean,
      default: false,
    },
    emailVerificationToken: String,
    passwordResetToken: String,
    passwordResetExpires: Date,
    refreshTokens: [
      {
        token: { type: String, required: true },
        expires: { type: Date, required: true },
        userAgent: String,
        ipAddress: String,
      }
    ],
    pushTokens: [pushTokenSchema], // Add the array field for push tokens
    age: {
      type: Number,
      min: [0, 'Age cannot be negative'],
    },
    familyMembers: [{
      name: { type: String, required: true },
      relationship: { type: String, required: true },
      age: { type: Number },
    }],
    workDetails: {
      location: String,
      workingHours: {
        start: String,
        end: String,
      },
    },
    homeLocation: String,
    frequentLocations: [String],
    preferences: {
      theme: { type: String, default: 'light' },
      language: { type: String, default: 'en' },
      notifications: { type: Boolean, default: true },
      grocery: {
        defaultListType: {
          type: String,
          enum: ['personal', 'shared'],
          default: 'personal'
        },
        defaultSharedListOwnerId: {
          type: String,
          default: null
        },
        autoSwitchToDefault: {
          type: Boolean,
          default: false
        },
        showPersonalListInSidebar: {
          type: Boolean,
          default: true
        }
      }
    },
    purposes: {
      type: [String],
      enum: ['Personal', 'Work', 'Education'],
      default: ['Personal'],
    },
  },
  {
    timestamps: true,
  }
);

// Add indexing for efficient token lookup
userSchema.index({ 'pushTokens.token': 1 });

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error: any) {
    next(error);
  }
});

// Method to compare password
userSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  // Check if password field exists on the current instance
  // It should exist if fetched correctly with .select('+password')
  if (!this.password) {
    throw new Error('Password field not available for comparison. Ensure user is fetched with password selected.');
    // return false;
  }

  const isMatch = await bcrypt.compare(candidatePassword, this.password);

  return isMatch;
};

// Method to create password reset token
userSchema.methods.createPasswordResetToken = function(): string {
  const resetToken = crypto.randomBytes(32).toString('hex');
  this.passwordResetToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');

  // Set expiry (10 minutes)
  this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000);

  return resetToken; // Return the raw token to be emailed
};

// Method to create email verification token
userSchema.methods.createEmailVerificationToken = function(): string {
  const verificationToken = crypto.randomBytes(32).toString('hex');
  this.emailVerificationToken = crypto
    .createHash('sha256')
    .update(verificationToken)
    .digest('hex');

  return verificationToken; // Return the raw token to be emailed
};

// Method to create refresh token
userSchema.methods.createRefreshToken = function(userAgent?: string, ipAddress?: string): string {
  const refreshToken = crypto.randomBytes(40).toString('hex');

  // Hash the token before storing
  const hashedToken = crypto
    .createHash('sha256')
    .update(refreshToken)
    .digest('hex');

  // Set expiry (7 days)
  const expiryDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);

  // Add to refreshTokens array
  this.refreshTokens = this.refreshTokens || [];
  this.refreshTokens.push({
    token: hashedToken,
    expires: expiryDate,
    userAgent,
    ipAddress
  });

  // Limit the number of refresh tokens per user (optional)
  const MAX_REFRESH_TOKENS = 5;
  if (this.refreshTokens.length > MAX_REFRESH_TOKENS) {
    // Remove oldest tokens if limit exceeded
    this.refreshTokens = this.refreshTokens.slice(-MAX_REFRESH_TOKENS);
  }

  return refreshToken; // Return the raw token
};

// Method to remove a refresh token
userSchema.methods.removeRefreshToken = async function(token: string): Promise<void> {
  // Hash the token to match the stored version
  const hashedToken = crypto
    .createHash('sha256')
    .update(token)
    .digest('hex');

  // Remove the token from the array
  if (this.refreshTokens && this.refreshTokens.length > 0) {
    this.refreshTokens = this.refreshTokens.filter((t: { token: string }) => t.token !== hashedToken);
    await this.save();
  }
};

// Method to add/update a push token
userSchema.methods.addPushToken = async function(token: string, deviceType: 'android' | 'ios' | 'web'): Promise<void> {
  if (!this.pushTokens) {
    this.pushTokens = [];
  }
  // Remove any existing token for the same device to avoid duplicates
  this.pushTokens = this.pushTokens.filter((pt: IPushToken) => pt.token !== token);
  // Add the new token
  this.pushTokens.push({ token, deviceType, createdAt: new Date() });
  // Optional: Limit the number of tokens per user
  const MAX_TOKENS_PER_USER = 10;
  if (this.pushTokens.length > MAX_TOKENS_PER_USER) {
    this.pushTokens.sort((a: IPushToken, b: IPushToken) => b.createdAt.getTime() - a.createdAt.getTime()); // Sort newest first
    this.pushTokens = this.pushTokens.slice(0, MAX_TOKENS_PER_USER); // Keep the newest N
  }
  await this.save();
  secureLogger.log(`Added/Updated push token for user ${this._id}`);
};

// Method to remove a push token
userSchema.methods.removePushToken = async function(token: string): Promise<void> {
  if (this.pushTokens) {
    const initialLength = this.pushTokens.length;
    this.pushTokens = this.pushTokens.filter((pt: IPushToken) => pt.token !== token);
    if (this.pushTokens.length < initialLength) {
      await this.save();
      secureLogger.log(`Removed push token for user ${this._id}`);
    }
  }
};

export const User = mongoose.model<IUser>('User', userSchema);