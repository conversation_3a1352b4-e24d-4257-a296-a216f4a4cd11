import React from 'react';
import {
  format,
  addHours,
  startOfDay,
  isWithinInterval,
  addMinutes,
} from 'date-fns';
import { CalendarSyncIndicator } from '../../tasks/CalendarSyncIndicator';
import { Task } from '../../../types/task';

interface DayViewProps {
  selectedDate: Date;
  filters: {
    categories: string[];
    priority: string;
    status: string;
  };
}

const DayView: React.FC<DayViewProps> = ({ selectedDate, filters }) => {
  const hours = Array.from({ length: 24 }, (_, i) => i);

  // Mock tasks - replace with actual task fetching
  const tasks: Task[] = [];

  const getTasksForHour = (hour: number) => {
    const hourStart = addHours(startOfDay(selectedDate), hour);
    const hourEnd = addMinutes(hourStart, 59);

    return tasks.filter(task => {
      const taskDate = new Date(task.deadline);
      return isWithinInterval(taskDate, { start: hourStart, end: hourEnd });
    });
  };

  return (
    <div className="bg-white rounded-lg shadow overflow-auto">
      <div className="flex">
        {/* Time column */}
        <div className="w-20 border-r border-gray-200 flex-shrink-0">
          {hours.map(hour => (
            <div
              key={hour}
              className="h-16 border-b border-gray-200 text-xs text-gray-500 text-right pr-2 pt-1"
            >
              {format(addHours(startOfDay(selectedDate), hour), 'h a')}
            </div>
          ))}
        </div>

        {/* Tasks column */}
        <div className="flex-1 min-w-[300px]">
          {hours.map(hour => {
            const tasksForHour = getTasksForHour(hour);
            return (
              <div
                key={hour}
                className="h-16 border-b border-gray-200 p-1 relative group hover:bg-gray-50"
              >
                {/* Add task button - only shows on hover */}
                <button className="absolute right-2 top-1 opacity-0 group-hover:opacity-100 text-xs text-blue-600 hover:text-blue-800">
                  + Add task
                </button>

                {tasksForHour.map(task => (
                  <div
                    key={task._id}
                    className="bg-white border border-blue-200 rounded p-2 mb-1 shadow-sm hover:shadow transition-shadow"
                  >
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-medium text-sm">{task.title}</span>
                      <CalendarSyncIndicator task={task} />
                    </div>
                    {task.content && (
                      <p className="text-xs text-gray-600 truncate">
                        {task.content}
                      </p>
                    )}
                    <div className="mt-1 flex items-center space-x-2">
                      {task.priority && (
                        <span
                          className={`text-xs px-1.5 py-0.5 rounded ${
                            task.priority === 'high'
                              ? 'bg-red-100 text-red-800'
                              : task.priority === 'medium'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-green-100 text-green-800'
                          }`}
                        >
                          {task.priority}
                        </span>
                      )}
                      {task.category && (
                        <span className="text-xs text-gray-500">
                          {task.category}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default DayView;
