import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { calendarService, CalendarConnection } from '../../lib/calendar-service';
import { useToast } from '../../hooks/use-toast';
import { Loader2, Check, AlertCircle, Calendar as CalendarIcon, Plus } from 'lucide-react';

export const CalendarSettings: React.FC = () => {
  const [connections, setConnections] = useState<CalendarConnection[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isConnecting, setIsConnecting] = useState(false);
  const [syncSettings, setSyncSettings] = useState({
    syncEnabled: true,
    defaultCalendarId: '',
    syncPastEvents: false,
    syncReminders: true,
  });
  const { toast } = useToast();

  // Load calendar connections and settings
  useEffect(() => {
    const loadData = async () => {
      try {
        const [conns] = await Promise.all([
          calendarService.getConnectedCalendars(),
          // In a real app, you would also load the user's sync settings here
        ]);
        
        setConnections(conns);
        
        // Set default calendar if available
        if (conns.length > 0) {
          const defaultCalendar = conns.find(c => c.isConnected) || conns[0];
          setSyncSettings(prev => ({
            ...prev,
            defaultCalendarId: defaultCalendar.id,
          }));
        }
      } catch (error) {
        console.error('Failed to load calendar settings:', error);
        toast({
          title: 'Error',
          description: 'Failed to load calendar settings. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleConnectGoogleCalendar = async () => {
    try {
      setIsConnecting(true);
      const { authUrl } = await calendarService.connectGoogleCalendar();
      
      // Open the OAuth flow in a popup
      const width = 600;
      const height = 800;
      const left = window.screen.width / 2 - width / 2;
      const top = window.screen.height / 2 - height / 2;
      
      window.open(
        authUrl,
        'Connect Google Calendar',
        `width=${width},height=${height},top=${top},left=${left}`
      );
      
      // Listen for the OAuth callback
      const handleMessage = (event: MessageEvent) => {
        if (event.data?.type === 'oauth-callback' && event.data.success) {
          // Reload connections
          calendarService.getConnectedCalendars().then(setConnections);
          window.removeEventListener('message', handleMessage);
          
          toast({
            title: 'Success',
            description: 'Successfully connected to Google Calendar!',
          });
        }
      };
      
      window.addEventListener('message', handleMessage);
    } catch (error) {
      console.error('Failed to connect Google Calendar:', error);
      toast({
        title: 'Error',
        description: 'Failed to connect Google Calendar. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async (connectionId: string) => {
    try {
      await calendarService.disconnectGoogleCalendar();
      setConnections(conns => conns.filter(c => c.id !== connectionId));
      
      toast({
        title: 'Success',
        description: 'Successfully disconnected calendar.',
      });
    } catch (error) {
      console.error('Failed to disconnect calendar:', error);
      toast({
        title: 'Error',
        description: 'Failed to disconnect calendar. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleSettingChange = async (key: keyof typeof syncSettings, value: any) => {
    const newSettings = { ...syncSettings, [key]: value };
    setSyncSettings(newSettings);
    
    try {
      // In a real app, you would save these settings to the backend
      // await calendarService.updateSyncSettings(newSettings);
      
      toast({
        title: 'Settings saved',
        description: 'Your calendar sync settings have been updated.',
      });
    } catch (error) {
      console.error('Failed to save settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2">Loading calendar settings...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Calendar Connections</h3>
        <p className="text-sm text-muted-foreground">
          Connect your calendars to sync tasks and events
        </p>
      </div>

      <div className="space-y-4">
        {connections.length > 0 ? (
          <div className="space-y-4">
            {connections.map((connection) => (
              <div 
                key={connection.id}
                className="flex items-center justify-between rounded-lg border p-4"
              >
                <div className="flex items-center space-x-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                    <CalendarIcon className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">{connection.displayName}</p>
                    <p className="text-sm text-muted-foreground">
                      {connection.email || 'No email'}
                      {connection.lastSyncedAt && ` • Last synced ${new Date(connection.lastSyncedAt).toLocaleString()}`}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {connection.isConnected ? (
                    <>
                      <div className="flex items-center text-sm text-green-600">
                        <Check className="mr-1 h-4 w-4" />
                        Connected
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDisconnect(connection.id)}
                        disabled={isConnecting}
                      >
                        Disconnect
                      </Button>
                    </>
                  ) : (
                    <Button
                      size="sm"
                      onClick={handleConnectGoogleCalendar}
                      disabled={isConnecting}
                    >
                      {isConnecting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Connecting...
                        </>
                      ) : (
                        <>
                          <Plus className="mr-2 h-4 w-4" />
                          Connect
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="rounded-lg border border-dashed p-8 text-center">
            <CalendarIcon className="mx-auto h-12 w-12 text-muted-foreground" />
            <h4 className="mt-4 font-medium">No calendars connected</h4>
            <p className="mb-4 text-sm text-muted-foreground">
              Connect your calendar to sync tasks and events
            </p>
            <Button onClick={handleConnectGoogleCalendar} disabled={isConnecting}>
              {isConnecting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Connecting...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Connect Google Calendar
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      {connections.some(c => c.isConnected) && (
        <div className="space-y-6 pt-6">
          <div>
            <h3 className="text-lg font-medium">Sync Settings</h3>
            <p className="text-sm text-muted-foreground">
              Configure how tasks are synced with your calendar
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between space-x-2">
              <div className="space-y-0.5">
                <Label htmlFor="sync-enabled">Enable Calendar Sync</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically sync tasks with your calendar
                </p>
              </div>
              <Switch
                id="sync-enabled"
                checked={syncSettings.syncEnabled}
                onCheckedChange={(checked) => handleSettingChange('syncEnabled', checked)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="default-calendar">Default Calendar</Label>
              <Select
                value={syncSettings.defaultCalendarId}
                onValueChange={(value) => handleSettingChange('defaultCalendarId', value)}
                disabled={!syncSettings.syncEnabled}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select a calendar" />
                </SelectTrigger>
                <SelectContent>
                  {connections
                    .filter(c => c.isConnected)
                    .map((calendar) => (
                      <SelectItem key={calendar.id} value={calendar.id}>
                        {calendar.displayName}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-between space-x-2">
              <div className="space-y-0.5">
                <Label htmlFor="sync-past-events">Sync Past Events</Label>
                <p className="text-sm text-muted-foreground">
                  Include past events when syncing with calendar
                </p>
              </div>
              <Switch
                id="sync-past-events"
                checked={syncSettings.syncPastEvents}
                onCheckedChange={(checked) => handleSettingChange('syncPastEvents', checked)}
                disabled={!syncSettings.syncEnabled}
              />
            </div>

            <div className="flex items-center justify-between space-x-2">
              <div className="space-y-0.5">
                <Label htmlFor="sync-reminders">Sync Reminders</Label>
                <p className="text-sm text-muted-foreground">
                  Set reminders for tasks in your calendar
                </p>
              </div>
              <Switch
                id="sync-reminders"
                checked={syncSettings.syncReminders}
                onCheckedChange={(checked) => handleSettingChange('syncReminders', checked)}
                disabled={!syncSettings.syncEnabled}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
