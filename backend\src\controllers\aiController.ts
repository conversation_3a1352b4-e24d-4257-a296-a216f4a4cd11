import { Request, Response, NextFunction } from 'express';
import axios from 'axios'; // Import axios for direct API calls
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold, GenerationConfig, SafetySetting } from '@google/generative-ai';
import { Category, ICategory } from '../models/Category';
import { Task, ITask } from '../models/Task';
import { GroceryItem } from '../models/GroceryItem'; // Import GroceryItem model
import { GroceryList } from '../models/GroceryList'; // Import GroceryList model
import { UserGroceryLibraryItem } from '../models/UserGroceryLibraryItem';
import { UserSettings } from '../models/UserSettings'; // Import UserSettings
import { locationSuggestionService } from '../services/locationSuggestionService'; // Import location service
import mongoose, { Types } from 'mongoose';
import { subMinutes, subHours, subDays, subWeeks, addMinutes, addHours, addDays, addWeeks } from 'date-fns';
import { getPendingInsightsForUser, updateInsightStatus } from '../services/insightService';
import { categorizeGroceryItem } from '../services/groceryCategoryService';
import { getAIProvider, AIMessage, AIGenerationConfig, safeJsonParse } from '../services/aiProviderService';
import { promptService } from '../services/promptService'; // Import PromptService
import { ReminderForModel } from '../types/reminder';
import { applyKeytagMappings } from '../utils/taskKeytagUtils';
import secureLogger from '../utils/secureLogger';
import Insight from '../models/Insight'; // Import Insight model for AI interpretation confirmations
import { handleNewInsightCreated } from '../services/insightNotificationService'; // Corrected import path
import {
  ProcessQuickAddRequest,
  QuickAddSuccessResponse
} from '../types/quickAdd';

// Type definitions
interface AIReminderInput {
  offsetValue: number | string;
  offsetUnit: 'minutes' | 'hours' | 'days' | 'weeks';
}

// Configuration constants
const GOOGLE_API_KEY = process.env.GOOGLE_API_KEY;
const BRAVE_API_KEY = process.env.BRAVE_API_KEY; // Get Brave API Key

if (!GOOGLE_API_KEY) {
  secureLogger.error('GOOGLE_API_KEY environment variable is not set.');
}
// Add check for Brave key only if needed for chatbot
// if (!BRAVE_API_KEY) {
//   console.warn('BRAVE_API_KEY environment variable is not set. Web search fallback will be disabled.');
// }

const genAI = GOOGLE_API_KEY ? new GoogleGenerativeAI(GOOGLE_API_KEY) : null;
const model = genAI ? genAI.getGenerativeModel({ model: 'gemini-1.5-flash-latest' }) : null;

const defaultGenerationConfig: GenerationConfig = {
  temperature: 0.7,
  topK: 1,
  topP: 1,
  maxOutputTokens: 2048,
};

const defaultSafetySettings: SafetySetting[] = [
  { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
  { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
  { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
  { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE },
];

// Default AI configuration
const defaultAIConfig: AIGenerationConfig = {
    temperature: 0.2,
    maxTokens: 3072,
    topK: 1,
    topP: 1,
};

// Helper Functions
async function findCategoryIdsByNames(categoryNames: string[], allUserCategories: ICategory[]): Promise<string[]> {
  const categoryIds: string[] = [];

  for (const name of categoryNames) {
    // First try to find an exact match
    let category = allUserCategories.find(c => c.name.toLowerCase() === name.toLowerCase());

    // If no exact match, try fuzzy matching
    if (!category) {
      category = allUserCategories.find(c =>
        c.name.toLowerCase().includes(name.toLowerCase()) ||
        name.toLowerCase().includes(c.name.toLowerCase())
      );
    }

    if (category) {
      categoryIds.push((category as any)._id.toString());
    }
  }

  return categoryIds;
}

function calculateReminderTime(
  deadline: Date,
  offsetValue: number,
  offsetUnit: 'minutes' | 'hours' | 'days' | 'weeks'
): Date | null {
  if (!deadline || !offsetValue || !offsetUnit) {
    secureLogger.log('Missing required parameters for calculateReminderTime:', {
      hasDeadline: !!deadline,
      offsetValue,
      offsetUnit
    });
    return null;
  }

  // Validate that deadline is actually a valid date
  if (!(deadline instanceof Date) || isNaN(deadline.getTime())) {
    secureLogger.error('Invalid deadline provided to calculateReminderTime:', deadline);
    return null;
  }

  try {
    // Create a fresh copy of the date to avoid modifying the original
    const deadlineCopy = new Date(deadline.getTime());

    switch (offsetUnit) {
      case 'minutes':
        return subMinutes(deadlineCopy, offsetValue);
      case 'hours':
        return subHours(deadlineCopy, offsetValue);
      case 'days':
        return subDays(deadlineCopy, offsetValue);
      case 'weeks':
        return subWeeks(deadlineCopy, offsetValue);
      default:
        secureLogger.log(`Unrecognized offsetUnit: ${offsetUnit}`);
        return null;
    }
  } catch (error) {
    secureLogger.error('Error calculating reminder time:', error);
    secureLogger.error('Input values:', { deadline: deadline.toISOString(), offsetValue, offsetUnit });
    return null;
  }
}

function addUniqueId(arr: mongoose.Types.ObjectId[] | undefined, idToAdd: mongoose.Types.ObjectId): mongoose.Types.ObjectId[] {
  const currentArr = arr || [];
  if (!currentArr.some(id => id.equals(idToAdd))) {
    currentArr.push(idToAdd);
  }
  return currentArr;
}

async function updateGroceryLibrary(userId: Types.ObjectId, itemNames: string[]): Promise<void> {
  if (!itemNames?.length) return;

  const normalizedNames = itemNames.map(name => name.trim().toLowerCase()).filter(name => name.length > 0);
  if (!normalizedNames.length) return;

  secureLogger.log(`Updating grocery library for user ${userId} with items:`, normalizedNames);
  try {
    const operations = normalizedNames.map(name => ({
      updateOne: {
        filter: { userId, itemName: name },
        update: {
          $inc: { addFrequency: 1 },
          $set: { lastAddedAt: new Date() }
        },
        upsert: true
      }
    }));

    const result = await UserGroceryLibraryItem.bulkWrite(operations);
    secureLogger.log(`Grocery library update result for user ${userId}:`, result);
  } catch (error) {
    secureLogger.error(`Error updating grocery library for user ${userId}:`, error);
  }
}

// Add this helper function near the top with other helper functions
function isValidDate(dateString: string): boolean {
  if (!dateString) return false;
  const timestamp = Date.parse(dateString);
  return !isNaN(timestamp);
}

// Add this with other helper functions
function parseDateString(dateString: string): Date | null {
  if (!dateString) return null;

  try {
    // Try the standard Date.parse approach first
    const timestamp = Date.parse(dateString);

    if (!isNaN(timestamp)) {
      return new Date(timestamp);
    }

    // Handle special relative date formats that might come from AI
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Start of today

    const lowerDateString = dateString.toLowerCase().trim();

    if (lowerDateString === 'today') {
      return today;
    } else if (lowerDateString === 'tomorrow') {
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow;
    } else if (lowerDateString === 'next week') {
      const nextWeek = new Date(today);
      nextWeek.setDate(nextWeek.getDate() + 7);
      return nextWeek;
    } else if (lowerDateString === 'next month') {
      const nextMonth = new Date(today);
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      nextMonth.setDate(1); // First day of next month
      return nextMonth;
    }

    // Handle common date formats that Date.parse sometimes fails on
    // Try DD/MM/YYYY or MM/DD/YYYY
    const dateParts = lowerDateString.split(/[\/.-]/);
    if (dateParts.length === 3) {
      const [part1, part2, part3] = dateParts.map(p => parseInt(p, 10));

      // Try assuming MM/DD/YYYY
      if (!isNaN(part1) && !isNaN(part2) && !isNaN(part3)) {
        // If part1 is clearly a month (1-12) and part2 could be a day (1-31)
        if (part1 >= 1 && part1 <= 12 && part2 >= 1 && part2 <= 31) {
          const mmddyyyy = new Date(part3, part1 - 1, part2);
          if (!isNaN(mmddyyyy.getTime())) return mmddyyyy;
        }

        // Try assuming DD/MM/YYYY
        if (part1 >= 1 && part1 <= 31 && part2 >= 1 && part2 <= 12) {
          const ddmmyyyy = new Date(part3, part2 - 1, part1);
          if (!isNaN(ddmmyyyy.getTime())) return ddmmyyyy;
        }
      }
    }

    // Could not parse the date
    secureLogger.warn('[AI_DATE_PARSING_FAILURE]', {
      originalDateString: dateString,
      context: 'aiController.parseDateString'
    });
    return null;
  } catch (error) {
    secureLogger.error('[AI_DATE_PARSING_ERROR]', {
      originalDateString: dateString,
      errorMessage: error.message,
      context: 'aiController.parseDateString'
    });
    return null;
  }
}

// --- Controller Functions ---

export const analyzeContent = async (req: Request, res: Response, next: NextFunction) => {
  const aiProvider = getAIProvider();
  if (!aiProvider) {
    return next(new Error('No AI provider available'));
  }

  const { content } = req.body;
  const userId = req.user?.userId;

  if (!userId) {
    res.status(401).json({ success: false, error: { message: 'User not authenticated.' } });
    return;
  }
  if (!content || typeof content !== 'string' || content.trim().length === 0) {
    res.status(400).json({ success: false, error: { message: 'Content is required for analysis.' } });
    return;
  }

  try {
    const allUserCategories = await Category.find({ userId });
    const categoryNamesList = allUserCategories.map(cat => cat.name).join(', ') || 'None'; // Prepare for prompt

    // Get prompt from service
    const systemMessage = promptService.getSystemMessage('analyzeContent');
    const userPrompt = promptService.formatPrompt('analyzeContent', {
      categoryNamesList: categoryNamesList,
      taskContent: content
    });

    if (!userPrompt) {
      secureLogger.error('[analyzeContent] Failed to format prompt: analyzeContent');
      return next(new Error('Internal server error: Could not format AI prompt.'));
    }

    const messages: AIMessage[] = [
      { role: 'system', content: systemMessage },
      { role: 'user', content: userPrompt }
    ];

    const result = await aiProvider.generateText(messages, defaultAIConfig);
    // Fix: Access result.text directly
    const suggestedNamesText = result.text ? result.text.trim() : '';

    secureLogger.debug("AI Raw Suggestion:", suggestedNamesText);
    const suggestedNames = suggestedNamesText ? suggestedNamesText.split(',').map(name => name.trim()).filter(name => name.length > 0) : [];
    const suggestedCategoryIds = await findCategoryIdsByNames(suggestedNames, allUserCategories);

    res.status(200).json({ success: true, data: { suggestedCategoryIds } });
  } catch (error: any) {
    secureLogger.error('Error during AI analysis:', error);
    if (error.message?.includes('API key not valid')) {
      res.status(401).json({ success: false, error: { message: 'Invalid API Key.' } });
      return;
    }
    next(error);
  }
};

// Helper function to select the best title
function selectBestTitle(aiTitle: string, aiSummaryTitle?: string | null): string {
  if (aiSummaryTitle && aiSummaryTitle.length > aiTitle.length && aiSummaryTitle.length < 80) {
    return aiSummaryTitle;
  }
  return aiTitle;
}

// Helper function to select the best description
function selectBestDescription(aiContent?: string | null, aiParsedDescription?: string | null, aiOriginalUtterance?: string | null): string | undefined {
  if (aiParsedDescription) return aiParsedDescription;
  if (aiContent && aiContent !== aiOriginalUtterance) return aiContent; // Prefer AI's refined content over raw utterance if different
  if (aiOriginalUtterance) return aiOriginalUtterance; // Fallback to original utterance
  return undefined;
}

// Updated function for processing quick add text input, now handling tasks OR groceries with enhanced AI capabilities
export const processQuickAdd = async (req: Request, res: Response, next: NextFunction) => {
  const aiProvider = getAIProvider();
  if (!aiProvider) {
    return next(new Error('No AI provider available'));
  }

  const { quickInputText, geolocation, context } = req.body;
  const userId = req.user?.userId;

  if (!userId) {
    res.status(401).json({ success: false, error: { message: 'User not authenticated.' } });
    return;
  }
  if (!quickInputText || typeof quickInputText !== 'string' || quickInputText.trim().length === 0) {
    res.status(400).json({ success: false, error: { message: 'Input text is required.' } });
    return;
  }

  // Extract geolocation if available
  const currentLat = geolocation?.latitude;
  const currentLon = geolocation?.longitude;

  if (currentLat && currentLon) {
    secureLogger.log(`Using geolocation for task creation: ${currentLat}, ${currentLon}`);
  } else {
    secureLogger.log('No geolocation provided for task creation');
  }

  try {
    // 1. Fetch user categories and settings
    const allUserCategories = await Category.find({ userId }).lean();
    const categoryNamesList = allUserCategories.map(cat => cat.name).join(', ') || 'None';
    const userSettings = await UserSettings.findOne({ userId }).lean();
    const autoMapEnabled = userSettings?.categoryLocationMapping?.autoMapWithoutConfirmation ?? true;

    // 2. Prepare AI messages using PromptService
    const systemMessage = promptService.getSystemMessage('processQuickAdd');
    const userPrompt = promptService.formatPrompt('processQuickAdd', {
      categoryNamesList: categoryNamesList,
      quickInputText: quickInputText,
      currentDateTime: new Date().toISOString()
    });

    if (!userPrompt) {
      secureLogger.error('[processQuickAdd] Failed to format prompt: processQuickAdd');
      return next(new Error('Internal server error: Could not format AI prompt.'));
    }

    const messages: AIMessage[] = [
      { role: 'system', content: systemMessage },
      { role: 'user', content: userPrompt }
    ];

    // 3. Get AI response
    const result = await aiProvider.generateText(messages, {
      ...defaultAIConfig,
      temperature: 0.3 // Lower temperature for more consistent parsing
    });

    // 4. Parse response - AI now returns a single JSON object
    const aiOutput = safeJsonParse(result.text);
    secureLogger.log('[processQuickAdd] Parsed AI Output:', JSON.stringify(aiOutput, null, 2));

    if (!aiOutput || typeof aiOutput !== 'object' || !Array.isArray(aiOutput.tasks)) {
      secureLogger.error('AI response was not a valid JSON object with a tasks array:', result.text);
      // It's better to inform the user about the parsing failure rather than throwing a generic error
      res.status(500).json({ success: false, error: { message: 'AI response format error. Could not parse tasks.' } });
      return;
    }

    const suggestedProjectTitle = aiOutput.suggestedProjectTitle || null;
    const tasksArrayFromAI = aiOutput.tasks;

    if (tasksArrayFromAI.length === 0) {
      secureLogger.log('AI returned an empty tasks array, indicating no specific intent found.');
      res.status(200).json({
        success: true,
        type: 'none',
        message: 'No actionable items found in the input.',
        data: [],
        suggestedProjectTitle: null // Ensure this is included even for 'none' type
      });
      return;
    }

    const firstItem = tasksArrayFromAI[0];
    if (!firstItem || !firstItem.intent) {
      secureLogger.error('First item in AI tasks array lacks an intent:', tasksArrayFromAI);
      res.status(500).json({ success: false, error: { message: 'AI response malformed: Missing intent.' } });
      return;
    }
    const intent = firstItem.intent;

    if (intent === 'grocery') {
      const groceryData = firstItem;
      if (!Array.isArray(groceryData.items) || groceryData.items.length === 0) {
        secureLogger.log('AI identified grocery intent but provided no valid items:', groceryData.items);
        res.status(400).json({ success: false, error: { message: 'Could not extract grocery items from input.' } });
        return;
      }

      // Get confidence level, original utterance, and autocorrection data from the new AI response format
      const aiConfidence = groceryData.confidence_level || 'Medium';
      const aiOriginalUtterance = groceryData.original_utterance_segment || quickInputText;
      const wasAutoCorrected = groceryData.was_auto_corrected || false;
      const originalTextBeforeCorrection = groceryData.original_text_before_correction;
      const correctedTextSegment = groceryData.corrected_text_segment;

      secureLogger.debug(`AI identified grocery intent with ${aiConfidence} confidence. ${wasAutoCorrected ? 'Text was autocorrected.' : ''} Items count:`, groceryData.items.length);
      try {
        const items = groceryData.items.map((item: any) => {
          const itemName = typeof item === 'string' ? item : item.name;
          const quantity = typeof item === 'object' && item.quantity ? item.quantity : undefined;
          return {
            name: itemName ? String(itemName).trim() : '',
            quantity: quantity ? String(quantity).trim() : undefined
          };
        }).filter((item: { name: string; quantity?: string }) => item.name.length > 0);

        if (items.length > 0) {
          const createdItems = [];

          // Determine target user ID based on context
          let targetUserId = userId;
          if (context?.groceryListOwnerId) {
            // Validate that the user has access to this shared list
            const groceryList = await GroceryList.findOne({ userId: context.groceryListOwnerId });
            if (groceryList && groceryList.isShared) {
              const userRole = groceryList.getUserRole(new Types.ObjectId(userId));
              if (userRole === 'editor' || userRole === 'owner') {
                targetUserId = context.groceryListOwnerId;
                secureLogger.log(`Quick add: Adding groceries to shared list owned by ${targetUserId} (user role: ${userRole})`);
              } else {
                secureLogger.warn(`Quick add: User ${userId} does not have edit access to shared list ${context.groceryListOwnerId}`);
              }
            }
          }

          for (const item of items) {
            const capitalizedName = item.name
              .split(' ')
              .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
              .join(' ');
            const categoryData = await categorizeGroceryItem(capitalizedName);
            const newItem = new GroceryItem({
              userId: new Types.ObjectId(targetUserId),
              name: capitalizedName,
              isChecked: false,
              category: { name: categoryData.name, color: categoryData.color },
              quantity: item.quantity,
              addedBy: targetUserId !== userId ? new Types.ObjectId(userId) : undefined
            });
            await newItem.save();
            createdItems.push(newItem);
          }

          // Create insight for grocery autocorrections if applicable
          if (wasAutoCorrected && originalTextBeforeCorrection && correctedTextSegment) {
            try {
              await Insight.create({
                userId: new Types.ObjectId(userId),
                type: 'AI_AUTOCORRECTION_REVIEW',
                description: `AI corrected text in grocery list. Original: "${originalTextBeforeCorrection}". Corrected: "${correctedTextSegment}".`,
                relatedData: {
                  originalText: originalTextBeforeCorrection,
                  correctedText: correctedTextSegment
                },
                status: 'pending'
              });
              secureLogger.log(`Created AI_AUTOCORRECTION_REVIEW insight for grocery list with correction from "${originalTextBeforeCorrection}" to "${correctedTextSegment}"`);
            } catch (insightError) {
              secureLogger.error('Failed to create insight for grocery autocorrection:', insightError);
            }
          }

          // Update grocery library in background (fire and forget)
          const itemNames = items.map((item: { name: string; quantity?: string }) => item.name);
          updateGroceryLibrary(new Types.ObjectId(targetUserId), itemNames).catch(err => {
            secureLogger.error(`Background grocery library update failed for user ${targetUserId}:`, err);
          });

          // If adding to a shared list, emit real-time updates and update activity
          if (targetUserId !== userId) {
            const groceryList = await GroceryList.findOne({ userId: targetUserId });
            if (groceryList) {
              // Import socketService if not already imported
              const { socketService } = require('../services/socketService');

              for (const item of createdItems) {
                socketService.emitGroceryItemAdded(
                  targetUserId,
                  item.toObject(),
                  new Types.ObjectId(userId)
                );
              }

              // Update list activity
              groceryList.lastCollaborativeActivity = new Date();
              await groceryList.save();
            }
          }

          res.status(201).json({ success: true, type: 'groceries', data: createdItems }); // No suggestedProjectTitle for groceries
        } else {
          res.status(400).json({ success: false, error: { message: 'No valid grocery items extracted.' } });
        }
      } catch (groceryError) {
        secureLogger.error(`Error adding grocery items for user ${userId}:`, groceryError);
        next(groceryError);
      }
      return; // Important to return after handling groceries
    } else if (intent === 'task') {
      const createdTasks: ITask[] = []; // Declare createdTasks here

      for (const taskData of tasksArrayFromAI) {
        // Per-task variable declarations
        let locationObjectId: Types.ObjectId | undefined = undefined;
        let processedDeadline: Date | null = null;
        let deadlineParsingFailed = false; // Renamed from aiSuggestedDeadlineFailed for clarity within loop

        // Ensure metadata is an object, even if taskData.metadata is undefined
        let finalTaskMetadata: Record<string, any> = { ...(taskData.metadata || {}) };

        if (taskData.intent !== 'task') {
          secureLogger.log('Skipping item in tasks array with non-task intent:', taskData);
          continue;
        }

        // Extract new AI fields from the enhanced response format including autocorrection data
        const {
          title,
          summary_title, // More descriptive title if main title is very concise
          content,
          parsed_description, // AI's structured description
          original_utterance_segment, // The segment of user input for this task
          confidence_level, // AI's confidence in parsing this task
          clarification_needed, // Question if AI is unsure about a detail
          was_auto_corrected, // NEW: Flag indicating if AI corrected spelling/typos
          original_text_before_correction, // NEW: The text before correction
          corrected_text_segment, // NEW: The text after correction
          deadline, // Expecting YYYY-MM-DD string or null from AI
          time,     // Expecting HH:MM string or null from AI
          priority = 'Medium',
          categoryNames = [],
          locationName, // AI might provide a location name string
          reminders = []
        } = taskData;

        // Store the new AI fields in metadata
        if (confidence_level) finalTaskMetadata.aiConfidence = confidence_level;
        if (clarification_needed) finalTaskMetadata.aiClarificationNeeded = clarification_needed;
        if (original_utterance_segment) finalTaskMetadata.aiOriginalUtterance = original_utterance_segment;
        if (summary_title) finalTaskMetadata.aiSummaryTitle = summary_title;
        if (parsed_description) finalTaskMetadata.aiParsedDescription = parsed_description;

        // Store autocorrection data if available
        if (was_auto_corrected) {
          finalTaskMetadata.aiWasAutoCorrected = true;
          if (original_text_before_correction) finalTaskMetadata.aiOriginalTextSegment = original_text_before_correction;
          if (corrected_text_segment) finalTaskMetadata.aiCorrectedTextSegment = corrected_text_segment;
        }

        if (!title || typeof title !== 'string' || title.trim() === "") {
          secureLogger.log('Skipping task creation due to missing or invalid title:', taskData);
          continue;
        }

        try {
          const categoryIds = await findCategoryIdsByNames(categoryNames, allUserCategories);
          const capitalizedTitle = title; // Assuming AI handles capitalization as per new prompt

          // Location Processing for this specific taskData
          try {
            secureLogger.log(`Attempting location suggestion for task "${capitalizedTitle}" with AI hint: "${locationName || 'None'}"`);
            const locationSuggestions = await locationSuggestionService.suggestLocations(
              userId, currentLat, currentLon, capitalizedTitle, content, categoryIds, locationName
            );

            if (Array.isArray(locationSuggestions) && locationSuggestions.length > 0) {
              const bestSuggestion = locationSuggestions[0];
              if (bestSuggestion._id && mongoose.isValidObjectId(bestSuggestion._id)) {
                locationObjectId = new Types.ObjectId(String(bestSuggestion._id));
                secureLogger.log(`Auto-mapped location for task "${capitalizedTitle}": ${bestSuggestion.name} (ID: ${locationObjectId})`);
              } else {
                secureLogger.log(`Best location suggestion for "${capitalizedTitle}" had invalid _id: ${bestSuggestion._id}`);
              }
            } else {
              secureLogger.log(`No suitable location suggestions found for task "${capitalizedTitle}".`);
            }
          } catch (locErr) {
            secureLogger.error(`Failed to process location suggestion for task "${capitalizedTitle}":`, locErr);
          }

          // Deadline and Time processing for this specific taskData
          if (deadline && typeof deadline === 'string') {
            processedDeadline = parseDateString(deadline);
            if (!processedDeadline) {
              secureLogger.warn(`[QuickAdd] AI provided deadline "${deadline}" for task "${title}" but it was not parsable.`);
              if (deadline.trim() !== "") { // Only store if AI provided something non-empty
                deadlineParsingFailed = true; // Local flag for this task
                finalTaskMetadata.aiSuggestedDeadlineFailed = deadline; // Store original unparsable string in metadata
              }
            } else {
              secureLogger.log(`Successfully parsed deadline for task "${title}": ${processedDeadline.toISOString()}`);
              if (time && typeof time === 'string') {
                if (/^\d{1,2}:\d{2}$/.test(time)) {
                  const [hours, minutes] = time.split(':').map(Number);
                  if (!isNaN(hours) && !isNaN(minutes) && hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59) {
                    processedDeadline.setHours(hours, minutes, 0, 0);
                    secureLogger.log(`Applied time "${time}" to deadline for task "${title}". New deadline: ${processedDeadline.toISOString()}`);
                  } else {
                    secureLogger.log(`Invalid time values for task "${title}": hours=${hours}, minutes=${minutes}`);
                  }
                } else {
                  secureLogger.log(`Invalid time format for task "${title}": ${time}`);
                }
              }
            }
          }

          // Reminders processing for this specific taskData
          const processedReminders: ReminderForModel[] = [];
          if (processedDeadline && !isNaN(processedDeadline.getTime()) && Array.isArray(reminders)) {
            for (const reminderInput of reminders) {
              if (reminderInput && typeof reminderInput.offsetValue === 'number' && typeof reminderInput.offsetUnit === 'string' &&
                  ['minutes', 'hours', 'days', 'weeks'].includes(reminderInput.offsetUnit)) {
                const calculatedReminderTime = calculateReminderTime( // Renamed to avoid conflict
                  processedDeadline,
                  reminderInput.offsetValue,
                  reminderInput.offsetUnit as 'minutes' | 'hours' | 'days' | 'weeks'
                );
                if (calculatedReminderTime) { // Check if a valid time was calculated
                  processedReminders.push({
                    type: 'notification',
                    offsetValue: reminderInput.offsetValue,
                    offsetUnit: reminderInput.offsetUnit as 'minutes' | 'hours' | 'days' | 'weeks',
                    reminderTime: calculatedReminderTime, // Use the calculated time
                    status: 'pending'
                  });
                } else {
                  secureLogger.log('Failed to calculate reminder time for task "'+title+'" with input:', reminderInput);
                }
              } else {
                secureLogger.log('Invalid reminder object structure from AI for task "'+title+'":', reminderInput);
              }
            }
          }

          // Use the helper functions to select the best title and description
          let finalTitle = selectBestTitle(title, summary_title);
          let finalContent = selectBestDescription(
            was_auto_corrected ? corrected_text_segment : content, // Use corrected content if available
            parsed_description,
            original_utterance_segment
          );

          const taskToSave = new Task({
            userId: new Types.ObjectId(userId),
            title: finalTitle,
            content: finalContent || '',
            deadline: processedDeadline && !isNaN(processedDeadline.getTime()) ? new Date(processedDeadline.getTime()) : null,
            priority: priority || 'Medium',
            categories: categoryIds.map(id => new Types.ObjectId(id)),
            location: locationObjectId,
            reminders: processedReminders,
            isAiGenerated: true,
            completed: false,
            metadata: finalTaskMetadata // Use the potentially updated metadata
          });

          const savedTask = await taskToSave.save();

          // Create insights for tasks with low/medium confidence, clarification needed, or autocorrections
          try {
            // Create insight for low/medium confidence or clarification needed
            if ((confidence_level === 'Low' || confidence_level === 'Medium') || clarification_needed) {
              await Insight.create({
                userId: new Types.ObjectId(userId),
                type: 'CONFIRM_AI_INTERPRETATION',
                description: `AI created task: "${finalTitle}". Original input: "${original_utterance_segment || quickInputText}". ${clarification_needed ? `AI asked: ${clarification_needed}` : 'Is this interpretation correct?'}`,
                relatedData: {
                  taskIds: [savedTask._id],
                  originalUtterance: original_utterance_segment || quickInputText,
                  aiConfidence: confidence_level || 'Medium'
                },
                status: 'pending'
              });
              secureLogger.log(`Created CONFIRM_AI_INTERPRETATION insight for task ${savedTask._id} with ${confidence_level} confidence`);
            }

            // Create insight for autocorrections
            if (was_auto_corrected && original_text_before_correction && corrected_text_segment) {
              await Insight.create({
                userId: new Types.ObjectId(userId),
                type: 'AI_AUTOCORRECTION_REVIEW',
                description: `AI corrected text in task: "${finalTitle}". Original: "${original_text_before_correction}". Corrected: "${corrected_text_segment}".`,
                relatedData: {
                  taskIds: [savedTask._id],
                  targetTaskId: savedTask._id,
                  originalText: original_text_before_correction,
                  correctedText: corrected_text_segment
                },
                status: 'pending'
              });
              secureLogger.log(`Created AI_AUTOCORRECTION_REVIEW insight for task ${savedTask._id} with correction from "${original_text_before_correction}" to "${corrected_text_segment}"`);
            }
          } catch (insightError) {
            secureLogger.error(`Failed to create insights for task ${savedTask._id}:`, insightError);
          }

          if (userSettings) {
            const taskToUpdateWithKeytags = savedTask.toObject();
            applyKeytagMappings(taskToUpdateWithKeytags, userSettings);
            const originalCategoriesSet = new Set(savedTask.categories.map(c => c.toString()));
            const updatedCategoriesSet = new Set(taskToUpdateWithKeytags.categories.map(c => c.toString()));
            let hasKeytagChanges = false;
            for (const category of updatedCategoriesSet) {
              if (!originalCategoriesSet.has(category)) {
                hasKeytagChanges = true;
                break;
              }
            }
            if (hasKeytagChanges) {
              secureLogger.log(`Task ${savedTask._id}: Adding categories from keytag mappings`);
              // Directly update the savedTask instance if possible, or re-fetch and update
              savedTask.categories = taskToUpdateWithKeytags.categories;
              await savedTask.save(); // Save again if categories were modified by keytags
            }
          }
          createdTasks.push(savedTask);
        } catch (taskError) {
          secureLogger.error(`Error creating individual task "${title || 'Untitled Task'}":`, taskError);
          // Continue to next taskData item
        }
      } // End of for...of loop for tasksArrayFromAI

      if (createdTasks.length > 0) {
        res.status(201).json({
          success: true,
          type: 'tasks',
          data: createdTasks,
          suggestedProjectTitle: suggestedProjectTitle
        });
      } else {
        res.status(400).json({
          success: false,
          error: { message: 'Could not create any valid tasks from the input.' },
          suggestedProjectTitle: suggestedProjectTitle
        });
      }
      return; // Important to return after handling tasks
    } else {
      secureLogger.error('Unknown intent type received from AI in tasks array:', intent);
      // Use a more specific error response
      res.status(500).json({ success: false, error: { message: `AI response error: Unknown intent '${intent}'.` } });
      return;
    }

  } catch (error) {
    secureLogger.error('Error processing quick add:', error);
    next(error);
  }
};


// New function to suggest priority for a task
export const suggestTaskPriority = async (req: Request, res: Response, next: NextFunction) => {
    // (Existing suggestTaskPriority function remains unchanged)
    if (!model) {
        return next(new Error('AI service is not configured (API key missing).'));
    }

    const { taskId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
        res.status(401).json({ success: false, error: { message: 'User not authenticated.' } });
        return; // Explicit return
    }
    if (!taskId || !mongoose.Types.ObjectId.isValid(taskId)) {
        res.status(400).json({ success: false, error: { message: 'Valid Task ID is required.' } });
        return; // Explicit return
    }

    try {
        // 1. Fetch the task
        const task = await Task.findOne({ _id: taskId, userId });
        if (!task) {
            res.status(404).json({ success: false, error: { message: 'Task not found or user not authorized.' } });
            return; // Explicit return
        }

        // 2. Craft Prompt for Priority Suggestion using PromptService
        const promptData = {
          taskTitle: task.title,
          taskContent: task.content || 'N/A',
          currentPriority: task.priority,
          deadline: task.deadline ? task.deadline.toISOString() : 'None',
          currentDateTime: new Date().toISOString()
        };

        const userPrompt = promptService.formatPrompt('suggestTaskPriority', promptData);
        const systemMessage = promptService.getSystemMessage('suggestTaskPriority'); // Get default or specific system message

        if (!userPrompt) {
          secureLogger.error(`[suggestTaskPriority] Failed to format prompt for task ${taskId}`);
          return next(new Error('Internal server error: Could not format AI prompt.'));
        }

        // Prepare messages for AI Provider Service (assuming it handles system messages)
        const messages: AIMessage[] = [
          { role: 'system', content: systemMessage }, // Include system message if provider uses it
          { role: 'user', content: userPrompt }
        ];

        // 3. Call AI Provider Service
        const aiProvider = getAIProvider();
        if (!aiProvider) {
          return next(new Error('No AI provider available'));
        }

        secureLogger.log(`Sending prompt to AI for priority suggestion (Task ID: ${taskId})...`);
        const result = await aiProvider.generateText(messages, {
             ...defaultAIConfig,
             maxTokens: 150 // Keep max tokens adjustment
        });
        // Note: The original code called Gemini directly. This now uses the aiProviderService.
        // Ensure aiProviderService is configured correctly.

        // 4. Parse Response
        // Fix: Access result.text directly
        const aiResponseText = result.text ? result.text.trim() : '';

        if (!aiResponseText) {
             secureLogger.error('AI Priority Suggestion response format unexpected or empty:', result);
             throw new Error('AI failed to generate priority suggestion.');
        }

        secureLogger.log("AI Raw Priority Suggestion Output:", aiResponseText);
        const parsedSuggestion = safeJsonParse(aiResponseText);

        if (!parsedSuggestion || typeof parsedSuggestion !== 'object' || !['Low', 'Medium', 'High', 'Critical'].includes(parsedSuggestion.suggestedPriority)) {
            secureLogger.log('AI returned invalid priority suggestion format:', parsedSuggestion);
            throw new Error('AI returned an invalid priority suggestion.');
        }

        // 5. Return Suggestion
        res.status(200).json({
            success: true,
            data: {
                taskId: task._id,
                currentPriority: task.priority,
                suggestedPriority: parsedSuggestion.suggestedPriority,
                reasoning: parsedSuggestion.reasoning || "No reasoning provided.", // Add default reasoning
                taskTitle: task.title // Include title for frontend display
            }
        });

    } catch (error: any) {
    secureLogger.error(`Error suggesting priority for task ${taskId}:`, error);
    if (error.message?.includes('API key not valid')) {
       res.status(401).json({ success: false, error: { message: 'Invalid Google API Key.' } });
       return; // Explicit return
    }
    res.status(500).json({ success: false, error: { message: 'Failed to get priority suggestion.' } });
    return; // Explicit return
    }
};

// --- Chatbot Controller ---

export const askChatbot = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const { question } = req.body;

    if (!userId) {
        res.status(401).json({ success: false, error: { message: 'User not authenticated.' } });
        return;
    }
    if (!question || typeof question !== 'string' || question.trim().length === 0) {
        res.status(400).json({ success: false, error: { message: 'Question is required.' } });
        return;
    }
    if (!model) {
        res.status(503).json({ success: false, error: { message: 'AI service is not configured (API key missing).' } });
        return;
    }

    try {
        // 1. Keyword search user's Tasks/Notes based on `question`.
        // Simple keyword extraction (split by space, could be improved)
        const keywords = question.toLowerCase().split(/\s+/).filter((kw: string) => kw.length > 2); // Basic filtering
        const searchRegex = new RegExp(keywords.join('|'), 'i'); // Case-insensitive OR search

        const relevantTasks = await Task.find({
            userId: userId,
            $or: [
                { title: { $regex: searchRegex } },
                { content: { $regex: searchRegex } }
            ]
        }).limit(10).select('title content createdAt deadline priority categories').lean(); // Limit results, select relevant fields

        // Fetch user's categories as well
        const userCategories = await Category.find({ userId: userId }).select('name').lean();
        const categoryListText = userCategories.length > 0
            ? userCategories.map((cat: any) => cat.name).join(', ')
            : "None";

        // 2. Prepare context for LLM (question + DB results + Categories).
        let taskContext = "No relevant tasks or notes found matching the keywords.";
        if (relevantTasks.length > 0) {
            taskContext = "Found the following potentially relevant tasks/notes:\n" +
                relevantTasks.map((task: any, index: number) =>
                    `- Task ${index + 1}: Title: ${task.title}, Content: ${task.content || 'N/A'}, Created: ${task.createdAt?.toDateString()}, Deadline: ${task.deadline?.toDateString() || 'None'}`
                ).join('\n');
        }

        const dbContext = `
User's Categories: ${categoryListText}

${taskContext}
        `.trim();


        // 3. Call LLM (via AI Provider) to attempt answer from context.
        const initialUserPrompt = promptService.formatPrompt('askChatbot_Initial', {
          dbContext: dbContext,
          question: question
        });
        const initialSystemMessage = promptService.getSystemMessage('askChatbot_Initial');

        if (!initialUserPrompt) {
          secureLogger.error('[askChatbot] Failed to format initial prompt');
          return next(new Error('Internal server error: Could not format AI prompt.'));
        }

        const initialMessages: AIMessage[] = [
          { role: 'system', content: initialSystemMessage },
          { role: 'user', content: initialUserPrompt }
        ];

        const aiProvider = getAIProvider();
        if (!aiProvider) {
          return next(new Error('No AI provider available'));
        }

        secureLogger.log(`Sending initial prompt to AI for question: "${question}"`);
        const initialResult = await aiProvider.generateText(initialMessages, defaultAIConfig);
        // Note: Original code used Gemini directly. Now using aiProviderService.

        // Fix: Access initialResult.text directly
        const initialResponseText = initialResult.text ? initialResult.text.trim() : '';

        if (!initialResponseText) {
            secureLogger.error('AI Chatbot initial response format unexpected or empty:', initialResult);
            throw new Error('AI failed to generate an initial response.');
        }

        secureLogger.log("AI Raw Initial JSON Output:", initialResponseText);
        const initialParsedResponse = safeJsonParse(initialResponseText);

        if (!initialParsedResponse || typeof initialParsedResponse !== 'object') {
             secureLogger.error('AI failed to return valid JSON for initial chatbot response:', initialParsedResponse);
             throw new Error('AI returned invalid JSON.');
        }

        // 4. Check if Web Search is needed
        if (initialParsedResponse.needsWebSearch === true) {
            secureLogger.log("Database context insufficient, proceeding to web search.");

            if (!BRAVE_API_KEY) {
                 secureLogger.log("BRAVE_API_KEY not set. Cannot perform web search.");
                 // Return the initial "couldn't find" message as the final answer
                 res.status(200).json({
                     success: true,
                     data: {
                         answer: initialParsedResponse.answer || "I couldn't find a relevant answer in your tasks/notes, and web search is not configured.",
                         source: "Database" // Source was the DB check
                     }
                 });
                 return;
            }

            // a. Formulate web search query
            const searchQuery = initialParsedResponse.searchQuery || question; // Use suggested query or original question
            secureLogger.log(`Performing Brave web search for: "${searchQuery}"`);

            // b. Call Brave Search API directly
            let webResultsText = "No results found or error during web search.";
            try {
                const braveResponse = await axios.get('https://api.search.brave.com/res/v1/web/search', {
                    headers: {
                        'Accept': 'application/json',
                        'Accept-Encoding': 'gzip',
                        'X-Subscription-Token': BRAVE_API_KEY
                    },
                    params: {
                        q: searchQuery,
                        count: 5 // Limit to 5 results for context
                    }
                });

                if (braveResponse.data?.web?.results && braveResponse.data.web.results.length > 0) {
                    webResultsText = "Found the following web search results:\n" +
                        braveResponse.data.web.results.map((result: any, index: number) =>
                            `- Result ${index + 1}: Title: ${result.title}, Snippet: ${result.description}, URL: ${result.url}`
                        ).join('\n');
                }
                 secureLogger.log("Brave Search Results Processed.");

            } catch (searchError: any) {
                secureLogger.error("Error calling Brave Search API:", searchError.response?.data || searchError.message);
                // Proceed without web results, maybe inform the user?
                webResultsText = "An error occurred while searching the web.";
            }

            // c. Prepare context (question + web results) using PromptService.
            const webSearchUserPrompt = promptService.formatPrompt('askChatbot_WebSearch', {
              webResultsText: webResultsText,
              question: question
            });
            const webSearchSystemMessage = promptService.getSystemMessage('askChatbot_WebSearch');

            if (!webSearchUserPrompt) {
              secureLogger.error('[askChatbot] Failed to format web search prompt');
              // Fallback: return the error message from Brave search or a generic one
              res.status(200).json({
                  success: true,
                  data: {
                      answer: webResultsText.startsWith("An error") ? webResultsText : "Failed to process web search results.",
                      source: "Web Search Error"
                  }
              });
              return;
            }

            const finalMessages: AIMessage[] = [
              { role: 'system', content: webSearchSystemMessage },
              { role: 'user', content: webSearchUserPrompt }
            ];

            // d. Call LLM (via AI Provider) again to synthesize answer from web results.
            secureLogger.log(`Sending web search results to AI for synthesis...`);
            const finalResult = await aiProvider.generateText(finalMessages, defaultAIConfig);
            // Note: Original code used Gemini directly. Now using aiProviderService.

            // Fix: Access finalResult.text directly
            let finalAnswerText = finalResult.text ? finalResult.text.trim() : '';

             if (!finalAnswerText) {
                 secureLogger.error('AI Chatbot final synthesis response format unexpected or empty:', finalResult);
                 // Use the error message or a default
                 finalAnswerText = webResultsText.startsWith("An error") ? webResultsText : 'I searched the web, but could not synthesize a final answer.';
             }

            // e. Format the final answer, stating the source as "Web Search".
            res.status(200).json({
                success: true,
                data: {
                    answer: finalAnswerText,
                    source: "Web Search" // Explicitly state source
                }
            });
            return; // Exit after web search path

        } else {
             // 5. Format the final answer (from DB context), stating the source.
             secureLogger.debug("Answer found using database context.");
             res.status(200).json({
                 success: true,
                 data: {
                     answer: initialParsedResponse.answer || "I found some relevant information in your database, but couldn't formulate a specific answer.",
                     source: "Database" // Explicitly state source
                 }
             });
        }

    } catch (error: any) { // Ensure error is typed as any or unknown
        secureLogger.error(`Error in askChatbot for user ${userId}:`, error);
        next(error);
    }
};


// --- Controller for Proactive Insights ---

export const getPendingInsights = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    if (!userId) {
        res.status(401).json({ success: false, error: { message: 'User not authenticated.' } });
        return;
    }

    // Extract and validate page and limit from query parameters
    let page = parseInt(req.query.page as string, 10) || 1;
    let limit = parseInt(req.query.limit as string, 10) || 5; // Default limit 5

    // Basic validation
    if (page < 1) page = 1;
    if (limit < 1) limit = 5; // Ensure limit is at least 1, default to 5 if invalid

    try {
        // Call the service function with pagination parameters
        const paginatedInsights = await getPendingInsightsForUser(
            new Types.ObjectId(userId),
            page,
            limit
        );
        // Return the paginated response structure { data: [...], pagination: {...} }
        res.status(200).json({ success: true, ...paginatedInsights }); // Spread the data and pagination keys
    } catch (error) {
        secureLogger.error(`Error fetching pending insights for user ${userId} (page: ${page}, limit: ${limit}):`, error);
        next(error); // Pass error to global error handler
    }
};

export const handleInsightAction = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const userId = req.user?.userId;
  const { insightId } = req.params;
  const { status } = req.body;

  if (!userId) {
    const error = { success: false, error: { message: 'User not authenticated.' } };
    secureLogger.error('[Insight Action] Authentication error');
    res.status(401).json(error);
    return;
  }

  if (!insightId) {
    const error = { success: false, error: { message: 'Insight ID is required but was not provided.' } };
    secureLogger.error('[Insight Action] Missing insightId');
    res.status(400).json(error);
    return;
  }

  if (!mongoose.Types.ObjectId.isValid(insightId)) {
    const error = { success: false, error: { message: `Invalid Insight ID format: ${insightId}` } };
    secureLogger.error('[Insight Action] Invalid insightId format');
    res.status(400).json(error);
    return;
  }

  if (!req.body) {
    const error = { success: false, error: { message: 'Request body is missing' } };
    secureLogger.error('[Insight Action] Missing request body');
    res.status(400).json(error);
    return;
  }

  if (status === undefined) {
    const error = { success: false, error: { message: 'Status field is missing in request body' } };
    secureLogger.error('[Insight Action] Missing status field');
    res.status(400).json(error);
    return;
  }

  if (status !== 'accepted' && status !== 'dismissed') {
    const error = { success: false, error: { message: `Invalid status value: '${status}'. Use "accepted" or "dismissed".` } };
    secureLogger.error('[Insight Action] Invalid status value');
    res.status(400).json(error);
    return;
  }

  try {
    const updatedInsight = await updateInsightStatus(
      new Types.ObjectId(insightId),
      new Types.ObjectId(userId),
      status as 'accepted' | 'dismissed'
    );

    if (!updatedInsight) {
      const error = { success: false, error: { message: 'Insight not found or user not authorized.' } };
      secureLogger.error('[Insight Action] Insight not found');
      res.status(404).json(error);
      return;
    }

    res.status(200).json({ success: true, data: updatedInsight });
  } catch (error) {
    secureLogger.error(`[Insight Action] Error processing action:`, error);
    next(error);
  }
};

// Fixed exports to avoid circular references
export { parseDateString, isValidDate };
export { calculateReminderTime };
