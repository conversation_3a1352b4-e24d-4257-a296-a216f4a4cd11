import mongoose, { Document, Schema, Types } from 'mongoose';

// Define interface for GroceryListInvitation document
export interface IGroceryListInvitation extends Document {
  groceryListId: Types.ObjectId;
  inviterUserId: Types.ObjectId;
  inviteeEmail: string;
  inviteeUserId?: Types.ObjectId; // Set when the invitee is found in the system
  role: 'editor' | 'viewer';
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  token: string;
  expiresAt: Date;
  acceptedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  
  // Methods
  isExpired(): boolean;
  markAsExpired(): Promise<void>;
}

// GroceryListInvitation Schema
const groceryListInvitationSchema = new Schema<IGroceryListInvitation>(
  {
    groceryListId: {
      type: Schema.Types.ObjectId,
      ref: 'GroceryList',
      required: true,
      index: true,
    },
    inviterUserId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true,
    },
    inviteeEmail: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
      index: true,
    },
    inviteeUserId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      index: true,
    },
    role: {
      type: String,
      enum: ['editor', 'viewer'],
      required: true,
      default: 'editor',
    },
    status: {
      type: String,
      enum: ['pending', 'accepted', 'declined', 'expired'],
      required: true,
      default: 'pending',
      index: true,
    },
    token: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    expiresAt: {
      type: Date,
      required: true,
      default: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      index: true,
    },
    acceptedAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
  }
);

// Compound indexes for efficient querying
groceryListInvitationSchema.index({ groceryListId: 1, status: 1 });
groceryListInvitationSchema.index({ inviteeEmail: 1, status: 1 });
groceryListInvitationSchema.index({ inviteeUserId: 1, status: 1 });
groceryListInvitationSchema.index({ expiresAt: 1, status: 1 });

// Method to check if invitation is expired
groceryListInvitationSchema.methods.isExpired = function(): boolean {
  return new Date() > this.expiresAt;
};

// Method to mark invitation as expired
groceryListInvitationSchema.methods.markAsExpired = async function(): Promise<void> {
  this.status = 'expired';
  await this.save();
};

// Pre-save middleware to automatically expire invitations
groceryListInvitationSchema.pre('save', function(next) {
  if (this.status === 'pending' && this.isExpired()) {
    this.status = 'expired';
  }
  next();
});

// Static method to clean up expired invitations
groceryListInvitationSchema.statics.cleanupExpired = async function() {
  const result = await this.updateMany(
    {
      status: 'pending',
      expiresAt: { $lt: new Date() }
    },
    {
      $set: { status: 'expired' }
    }
  );
  return result.modifiedCount;
};

export const GroceryListInvitation = mongoose.model<IGroceryListInvitation>(
  'GroceryListInvitation',
  groceryListInvitationSchema
);
