import { google, Auth, calendar_v3 } from 'googleapis';
import { IUser } from '../models/User';
import { ITask, Task } from '../models/Task';
import secureLogger from '../utils/secureLogger';
import { Types } from 'mongoose';

const GOOGLE_CALENDAR_CLIENT_ID = process.env.GOOGLE_CALENDAR_CLIENT_ID || '';
const GOOGLE_CALENDAR_CLIENT_SECRET = process.env.GOOGLE_CALENDAR_CLIENT_SECRET || '';
const GOOGLE_CALENDAR_REDIRECT_URI = `${process.env.API_BASE_URL || 'http://localhost:3000'}/api/auth/google/callback/calendar`;
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3000';

// Validate required environment variables
if (!GOOGLE_CALENDAR_CLIENT_ID || !GOOGLE_CALENDAR_CLIENT_SECRET) {
  console.error('Missing required Google Calendar OAuth credentials');
  process.exit(1);
}

// Utility functions
const toStringValue = (value: string | Types.ObjectId | undefined): string | undefined => {
  if (!value) return undefined;
  return value instanceof Types.ObjectId ? value.toString() : value;
};

const getUserTimezone = (user: IUser): string => {
  return user.preferences?.timezone || 'UTC';
};

class GoogleCalendarService {
  private getOAuth2Client(userTokens?: IUser['googleCalendarTokens']): Auth.OAuth2Client {
    const oauth2Client = new google.auth.OAuth2(
      GOOGLE_CALENDAR_CLIENT_ID,
      GOOGLE_CALENDAR_CLIENT_SECRET,
      GOOGLE_CALENDAR_REDIRECT_URI
    );

    if (userTokens?.accessToken) {
      oauth2Client.setCredentials({
        access_token: userTokens.accessToken,
        refresh_token: userTokens.refreshToken,
        expiry_date: userTokens.expiryDate,
        scope: userTokens.scope,
      });
    }
    return oauth2Client;
  }

  private async getCalendarApi(user: IUser): Promise<calendar_v3.Calendar | null> {
    if (!user.googleCalendarTokens?.accessToken) {
      secureLogger.warn(`[GoogleCalendarService] User ${user._id} has no Google Calendar access token.`);
      return null;
    }

    const oauth2Client = this.getOAuth2Client(user.googleCalendarTokens);

    // Handle token refresh if needed
    if (user.googleCalendarTokens.expiryDate && user.googleCalendarTokens.expiryDate < Date.now() + 60000) {
      if (user.googleCalendarTokens.refreshToken) {
        try {
          secureLogger.log(`[GoogleCalendarService] Refreshing Google Calendar token for user ${user._id}`);
          const refreshResponse = await oauth2Client.refreshAccessToken();
          // Handle different response formats
          const tokens = (refreshResponse as any).tokens || refreshResponse;
          oauth2Client.setCredentials(tokens);
          
          // Update user's tokens in DB
          user.googleCalendarTokens.accessToken = tokens.access_token!;
          if (tokens.refresh_token) user.googleCalendarTokens.refreshToken = tokens.refresh_token;
          user.googleCalendarTokens.expiryDate = tokens.expiry_date!;
          await user.save();
          
          secureLogger.log(`[GoogleCalendarService] Token refreshed and saved for user ${user._id}`);
        } catch (refreshError: any) {
          secureLogger.error(`[GoogleCalendarService] Failed to refresh Google Calendar token for user ${user._id}:`, refreshError);
          user.googleCalendarSyncEnabled = false;
          user.googleCalendarTokens = undefined;
          await user.save();
          return null;
        }
      } else {
        secureLogger.warn(`[GoogleCalendarService] Access token expired for user ${user._id}, but no refresh token available.`);
        user.googleCalendarSyncEnabled = false;
        user.googleCalendarTokens = undefined;
        await user.save();
        return null;
      }
    }

    return google.calendar({ version: 'v3', auth: oauth2Client });
  }

  async createEvent(user: IUser, task: ITask): Promise<string | null> {
    const calendar = await this.getCalendarApi(user);
    if (!calendar || !task.deadline) return null;

    try {
      // Get user's calendar settings with defaults
      const settings = user.calendarSettings || {
        syncEnabled: true,
        syncReminders: true,
        syncPastEvents: false
      };

      // Skip if sync is disabled
      if (!settings.syncEnabled) {
        secureLogger.log(`[GoogleCalendarService] Calendar sync is disabled for user ${user._id}`);
        return null;
      }

      // Skip past events if syncPastEvents is false
      const now = new Date();
      const taskDate = new Date(task.deadline);
      if (taskDate < now && !settings.syncPastEvents) {
        secureLogger.log(`[GoogleCalendarService] Skipping past task ${task._id} for user ${user._id}`);
        return null;
      }

      // Calculate event times
      const startTime = taskDate;
      const endTime = task.duration 
        ? new Date(startTime.getTime() + task.duration * 60000) // duration in minutes to ms
        : new Date(startTime.getTime() + 60 * 60 * 1000); // Default 1 hour

      // Build event description
      let description = task.content || '';
      if (task.location) {
        const locationId = toStringValue(task.location);
        const location = locationId ? await this.getLocationDetails(locationId) : null;
        description += `\n\nLocation: ${location?.name || locationId}`;
      }
      description += `\n\nView in Task OrganAIzer: ${FRONTEND_URL}/task/${task._id}`;

      // Build event object
      const event: calendar_v3.Schema$Event = {
        summary: task.title,
        description: description.trim(),
        start: {
          dateTime: startTime.toISOString(),
          timeZone: getUserTimezone(user),
        },
        end: {
          dateTime: endTime.toISOString(),
          timeZone: getUserTimezone(user),
        },
        reminders: settings.syncReminders ? {
          useDefault: true,
        } : undefined,
        source: {
          title: 'Task OrganAIzer',
          url: `${FRONTEND_URL}/task/${task._id}`
        },
        // Add task ID to extended properties for easier lookup
        extendedProperties: {
          private: {
            taskId: task._id ? task._id.toString() : '',
            source: 'task-organaizer'
          }
        }
      };

      // Add location if available
      if (task.location) {
        event.location = toStringValue(task.location);
      }

      // Create the event
      const calendarId = user.calendarSettings?.defaultCalendarId || user.googleCalendarId || 'primary';
      const response = await calendar.events.insert({
        calendarId,
        requestBody: event,
        conferenceDataVersion: 1, // Support for Google Meet links
        sendUpdates: 'all', // Send notifications to attendees if any
      });

      if (response.data.id) {
        secureLogger.log(`[GoogleCalendarService] Created event ${response.data.id} for task ${task._id}`);
        
        // Update task with calendar event ID
        await Task.findByIdAndUpdate(task._id, {
          calendarEventId: response.data.id,
          calendarSyncStatus: 'synced',
          calendarLastSyncedAt: new Date(),
          calendarProvider: 'google',
          $unset: { 'metadata.googleCalendarEventId': 1 } // Remove old field if exists
        });
        
        return response.data.id;
      }
      
      return null;
    } catch (error: any) {
      secureLogger.error(`[GoogleCalendarService] Failed to create event for task ${task._id}:`, error);
      
      // Update task with error status
      await Task.findByIdAndUpdate(task._id, {
        calendarSyncStatus: 'failed',
        calendarLastError: error.message,
        calendarLastSyncedAt: new Date()
      });
      
      return null;
    }
  }

  async updateEvent(user: IUser, task: ITask, eventId: string): Promise<boolean> {
    const calendar = await this.getCalendarApi(user);
    if (!calendar || !task.deadline) return false;

    try {
      // Get user's calendar settings with defaults
      const settings = user.calendarSettings || {
        syncEnabled: true,
        syncReminders: true,
        syncPastEvents: false
      };

      // Skip if sync is disabled
      if (!settings.syncEnabled) {
        secureLogger.log(`[GoogleCalendarService] Calendar sync is disabled for user ${user._id}`);
        return false;
      }

      // Skip past events if syncPastEvents is false
      const now = new Date();
      const taskDate = new Date(task.deadline);
      if (taskDate < now && !settings.syncPastEvents) {
        secureLogger.log(`[GoogleCalendarService] Skipping update for past task ${task._id}`);
        return false;
      }

      // Calculate event times
      const startTime = taskDate;
      const endTime = task.duration 
        ? new Date(startTime.getTime() + task.duration * 60000) // duration in minutes to ms
        : new Date(startTime.getTime() + 60 * 60 * 1000); // Default 1 hour

      // Build event description
      let description = task.content || '';
      if (task.location) {
        const locationId = toStringValue(task.location);
        const location = locationId ? await this.getLocationDetails(locationId) : null;
        description += `\n\nLocation: ${location?.name || locationId}`;
      }
      description += `\n\nView in Task OrganAIzer: ${FRONTEND_URL}/task/${task._id}`;

      // Build event object
      const event: calendar_v3.Schema$Event = {
        summary: task.title,
        description: description.trim(),
        start: {
          dateTime: startTime.toISOString(),
          timeZone: getUserTimezone(user),
        },
        end: {
          dateTime: endTime.toISOString(),
          timeZone: getUserTimezone(user),
        },
        reminders: settings.syncReminders ? {
          useDefault: true,
        } : undefined,
        source: {
          title: 'Task OrganAIzer',
          url: `${FRONTEND_URL}/task/${task._id}`
        },
        // Ensure we keep the existing extended properties
        extendedProperties: {
          private: {
            taskId: task._id ? task._id.toString() : '',
            source: 'task-organaizer',
            // Preserve any existing extended properties
            ...((task as any).calendarEventId ? { eventId: (task as any).calendarEventId } : {})
          }
        }
      };

      // Add location if available
      if (task.location) {
        event.location = toStringValue(task.location);
      }

      // Update the event
      const calendarId = user.calendarSettings?.defaultCalendarId || user.googleCalendarId || 'primary';
      await calendar.events.update({
        calendarId,
        eventId,
        requestBody: event,
        sendUpdates: 'all',
      });

      // Update task sync status
      await Task.findByIdAndUpdate(task._id, {
        calendarSyncStatus: 'synced',
        calendarLastSyncedAt: new Date(),
        $unset: { 'metadata.googleCalendarEventId': 1 } // Remove old field if exists
      });

      secureLogger.log(`[GoogleCalendarService] Updated event ${eventId} for task ${task._id}`);
      return true;
    } catch (error: any) {
      secureLogger.error(`[GoogleCalendarService] Failed to update event ${eventId} for task ${task._id}:`, error);
      
      // Update task with error status
      await Task.findByIdAndUpdate(task._id, {
        calendarSyncStatus: 'failed',
        calendarLastError: error.message,
        calendarLastSyncedAt: new Date()
      });
      
      return false;
    }
  }

  async deleteEvent(user: IUser, eventId: string): Promise<boolean> {
    const calendar = await this.getCalendarApi(user);
    if (!calendar) return false;

    try {
      // Get the calendar ID from user settings or use default
      const calendarId = user.calendarSettings?.defaultCalendarId || user.googleCalendarId || 'primary';
      
      // First, try to get the event to check if it exists
      try {
        await calendar.events.get({
          calendarId,
          eventId,
        });
      } catch (error: any) {
        // If event is not found (404), it's already deleted
        if (error.code === 404) {
          secureLogger.log(`[GoogleCalendarService] Event ${eventId} not found, assuming already deleted`);
          return true;
        }
        throw error; // Re-throw other errors
      }

      // If we get here, the event exists, so delete it
      await calendar.events.delete({
        calendarId,
        eventId,
        sendUpdates: 'all',
      });

      secureLogger.log(`[GoogleCalendarService] Deleted event ${eventId}`);
      return true;
    } catch (error: any) {
      // Don't fail hard if the event is already deleted
      if (error.code === 404) {
        secureLogger.log(`[GoogleCalendarService] Event ${eventId} not found during deletion`);
        return true;
      }
      
      secureLogger.error(`[GoogleCalendarService] Failed to delete event ${eventId}:`, error);
      return false;
    }
  }

  async getAuthUrl(): Promise<string> {
    const oauth2Client = this.getOAuth2Client();
    const scopes = [
      'https://www.googleapis.com/auth/calendar.events',
      'https://www.googleapis.com/auth/calendar',
    ];

    return oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent',
    });
  }

  async handleAuthCallback(code: string): Promise<Auth.Credentials | null> {
    const oauth2Client = this.getOAuth2Client();

    try {
      const tokenResponse = await oauth2Client.getToken(code);
      
      // Get the tokens from the response
      const tokens = (tokenResponse as any).tokens || tokenResponse;
      
      if (!tokens || !tokens.access_token) {
        throw new Error('No access token in response');
      }
      
      // Get user info to store with the token
      try {
        const oauth2 = google.oauth2({
          version: 'v2',
          auth: oauth2Client
        });
        
        oauth2Client.setCredentials(tokens);
        const userInfo = await oauth2.userinfo.get();
        
        // Add user info to tokens
        return {
          ...tokens,
          // Add user info as custom properties (type assertion needed)
          email: userInfo.data.email as any,
          name: userInfo.data.name as any,
          picture: userInfo.data.picture as any
        };
      } catch (userInfoError) {
        secureLogger.error('[GoogleCalendarService] Failed to get user info:', userInfoError);
        // Still return tokens even if we couldn't get user info
        return tokens;
      }
    } catch (error: any) {
      secureLogger.error('[GoogleCalendarService] Failed to get tokens from auth code:', error);
      return null;
    }
  }
  
  // Helper method to get location details (can be expanded)
  private async getLocationDetails(locationId: string): Promise<{ name: string; address?: string } | null> {
    try {
      // In a real implementation, this would fetch location details from your database
      // For now, we'll just return a simple object with the ID as the name
      return {
        name: locationId,
        address: 'Address not available'
      };
    } catch (error) {
      secureLogger.error(`[GoogleCalendarService] Failed to get location details for ${locationId}:`, error);
      return null;
    }
  }
}

export const googleCalendarService = new GoogleCalendarService();
