import mongoose, { Document, Schema, Types } from 'mongoose';

// Define interface for a single reminder subdocument
interface IReminder {
  _id?: Types.ObjectId; // Optional: Mongoose can auto-generate if needed, or manage manually
  type: 'notification' | 'email';
  offsetValue: number;
  offsetUnit: 'minutes' | 'hours' | 'days' | 'weeks';
  reminderTime: Date; // Calculated absolute time for the reminder
  status: 'pending' | 'sent' | 'failed';
  sentAt?: Date;
}

// Define the main Task interface including the reminders array
export interface ITask extends Document {
  userId: Types.ObjectId;
  title: string;
  content: string;
  categories: Types.ObjectId[];
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  deadline?: Date;
  duration?: number; // Duration in minutes
  location?: Types.ObjectId | string; // Can be ObjectId or string for external references
  isAiGenerated: boolean;
  completed: boolean;
  completedAt?: Date;
  reminders?: IReminder[]; // Array of reminder subdocuments
  dependsOn?: Types.ObjectId[]; // Tasks that must be completed before this one
  blocks?: Types.ObjectId[]; // Tasks that cannot start until this one is completed

  // Calendar sync fields
  calendarEventId?: string; // ID of the associated calendar event
  calendarSyncStatus?: 'pending' | 'synced' | 'failed' | 'not_synced'; // Status of calendar sync
  calendarLastSyncedAt?: Date; // Last time this task was synced with calendar
  calendarLastError?: string; // Last error message if sync failed
  calendarProvider?: 'google' | 'outlook' | 'apple' | 'other'; // Calendar provider

  // Timestamps are automatically added by Mongoose
  createdAt: Date;
  updatedAt: Date;

  metadata?: {
    sequence?: number;
    dependsOn?: string[];
    isBlocker?: boolean;
    aiSuggestedDeadlineFailed?: string; // Store the original unparsable deadline string
    aiConfidence?: 'High' | 'Medium' | 'Low'; // Store AI's confidence level in parsing this task
    aiClarificationNeeded?: string; // Store any clarification question the AI had
    aiOriginalUtterance?: string; // Store the segment of user input AI used for this task
    aiSummaryTitle?: string; // Store AI's summary title if different from main title
    aiParsedDescription?: string; // Store AI's parsed/structured description
    aiWasAutoCorrected?: boolean; // Flag indicating AI made a spelling/typo correction
    aiOriginalTextSegment?: string; // The text segment before AI correction
    aiCorrectedTextSegment?: string; // The text segment after AI correction
    [key: string]: any; // Allow for any additional metadata
  };
  aiSuggestions?: {
    categoryIds?: Types.ObjectId[];
    priority?: string;
    deadline?: Date;
    // Potentially add suggested reminders here later
  };
  orderData?: {
    position: number;
    contextId: string;
    lastUpdated: Date;
  };
  // Mongoose automatically adds createdAt and updatedAt
}

// Define the schema for the reminder subdocument
const reminderSchema = new Schema<IReminder>({
  type: {
    type: String,
    enum: ['notification', 'email'],
    required: true,
    default: 'notification'
  },
  offsetValue: {
    type: Number,
    required: true
  },
  offsetUnit: {
    type: String,
    enum: ['minutes', 'hours', 'days', 'weeks'],
    required: true
  },
  reminderTime: {
    type: Date,
    required: true,
    index: true // Index for potentially querying reminders later
  },
  status: {
    type: String,
    enum: ['pending', 'sent', 'failed'],
    default: 'pending',
    index: true
  },
  sentAt: { type: Date }
}, { _id: true }); // Mongoose will add _id by default, which can be useful for managing individual reminders

// Define the main task schema
const taskSchema = new Schema<ITask>(
  {
    // Calendar sync fields
    calendarEventId: { type: String, index: true },
    calendarSyncStatus: {
      type: String,
      enum: ['pending', 'synced', 'failed', 'not_synced'],
      default: 'not_synced'
    },
    calendarLastSyncedAt: Date,
    calendarLastError: String,
    calendarProvider: {
      type: String,
      enum: ['google', 'outlook', 'apple', 'other'],
      default: 'google'
    },

    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    title: {
      type: String,
      required: [true, 'Title is required'],
      trim: true,
    },
    content: {
      type: String,
    },
    categories: [{
      type: Schema.Types.ObjectId,
      ref: 'Category',
    }],
    priority: {
      type: String,
      enum: ['Low', 'Medium', 'High', 'Critical'],
      default: 'Medium',
    },
    deadline: {
      type: Date,
    },
    location: {
      type: Schema.Types.ObjectId,
      ref: 'Location',
    },
    completed: {
      type: Boolean,
      default: false,
    },
    completedAt: {
      type: Date,
    },
    isAiGenerated: {
      type: Boolean,
      default: false,
    },
    metadata: {
      type: Schema.Types.Mixed, // Allow flexible metadata structure
      default: {},
    },
    // Removed duplicated reminders and aiSuggestions from here
    dependsOn: [{                 // Added dependsOn
      type: Schema.Types.ObjectId,
      ref: 'Task',
    }],
    blocks: [{
      type: Schema.Types.ObjectId,
      ref: 'Task',
    }],
    reminders: [reminderSchema], // Correct placement for reminders
    aiSuggestions: {             // Correct placement for aiSuggestions
      categoryIds: [{ type: Schema.Types.ObjectId, ref: 'Category' }],
      priority: String,
      deadline: Date,
    },
    orderData: {
      position: { type: Number, default: 0 },
      contextId: { type: String, default: 'global' },
      lastUpdated: { type: Date, default: Date.now }
    },
  }, // End of main schema definition object
  {
    timestamps: true, // Adds createdAt and updatedAt automatically
  }
);

// Index for better query performance
taskSchema.index({ userId: 1, completed: 1 });
taskSchema.index({ categories: 1 });
taskSchema.index({ 'reminders.reminderTime': 1, 'reminders.status': 1 }); // Index for querying pending reminders
taskSchema.index({ dependsOn: 1 }); // Index for querying dependencies
taskSchema.index({ blocks: 1 }); // Index for querying blockers
taskSchema.index({ 'orderData.contextId': 1, 'orderData.position': 1 });

// Add middleware to handle empty string locations
taskSchema.pre('save', function(next) {
  // Convert empty location or empty string representation to null to avoid casting errors
  if (this.location !== undefined &&
      (this.location === null ||
       String(this.location).trim() === '' ||
       this.location instanceof mongoose.Types.ObjectId && this.location.toString() === '')) {
    this.location = undefined;
  }
  next();
});

// Add middleware to handle empty string locations in queries
taskSchema.pre('find', function() {
  const query = this.getQuery();
  // If there's an exact match for empty string location, replace it with null
  if (query.location === '') {
    query.location = null;
  }
});

// Also handle empty string locations in findOne, updateOne, etc.
taskSchema.pre(['findOne', 'updateOne', 'updateMany'], function() {
  const query = this.getQuery();
  // If there's an exact match for empty string location, replace it with null
  if (query.location === '') {
    query.location = null;
  }
});

export const Task = mongoose.model<ITask>('Task', taskSchema);
