import { body, ValidationChain } from 'express-validator';

// Validation schema for enabling sharing
export const enableSharingSchema: ValidationChain[] = [
  body('shareSettings').isObject().withMessage('Share settings must be an object'),
  body('shareSettings.allowCollaboratorInvites').optional().isBoolean().withMessage('allowCollaboratorInvites must be a boolean'),
  body('shareSettings.requireApprovalForEdits').optional().isBoolean().withMessage('requireApprovalForEdits must be a boolean'),
  body('shareSettings.notifyOnChanges').optional().isBoolean().withMessage('notifyOnChanges must be a boolean'),
];

// Validation schema for updating share settings
export const updateShareSettingsSchema: ValidationChain[] = [
  body('shareSettings').isObject().withMessage('Share settings must be an object'),
  body('shareSettings.allowCollaboratorInvites').optional().isBoolean().withMessage('allowCollaboratorInvites must be a boolean'),
  body('shareSettings.requireApprovalForEdits').optional().isBoolean().withMessage('requireApprovalForEdits must be a boolean'),
  body('shareSettings.notifyOnChanges').optional().isBoolean().withMessage('notifyOnChanges must be a boolean'),
];

// Validation schema for adding a collaborator
export const addCollaboratorSchema: ValidationChain[] = [
  body('email').isEmail().withMessage('Valid email is required'),
  body('role').optional().isIn(['editor', 'viewer']).withMessage('Role must be either editor or viewer'),
];

// Validation schema for updating collaborator role
export const updateCollaboratorRoleSchema: ValidationChain[] = [
  body('role').isIn(['editor', 'viewer']).withMessage('Role must be either editor or viewer'),
];

// Validation schema for inviting by email
export const inviteByEmailSchema: ValidationChain[] = [
  body('email').isEmail().withMessage('Valid email is required'),
  body('role').optional().isIn(['editor', 'viewer']).withMessage('Role must be either editor or viewer'),
];

// Validation schema for grocery list name update
export const updateGroceryListSchema: ValidationChain[] = [
  body('name').optional().isString().isLength({ max: 100 }).withMessage('Name must be a string with max 100 characters'),
  body('shareSettings').optional().isObject().withMessage('Share settings must be an object'),
  body('shareSettings.allowCollaboratorInvites').optional().isBoolean().withMessage('allowCollaboratorInvites must be a boolean'),
  body('shareSettings.requireApprovalForEdits').optional().isBoolean().withMessage('requireApprovalForEdits must be a boolean'),
  body('shareSettings.notifyOnChanges').optional().isBoolean().withMessage('notifyOnChanges must be a boolean'),
];
