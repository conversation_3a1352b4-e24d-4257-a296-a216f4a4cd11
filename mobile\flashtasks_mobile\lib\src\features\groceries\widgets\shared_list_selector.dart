import 'package:flutter/material.dart';
import '../models/grocery_list.dart';

/// Widget for selecting between shared grocery lists
class SharedListSelector extends StatelessWidget {
  final List<GroceryList> sharedLists;
  final String? currentListOwnerId;
  final Function(String? listOwnerId) onListSelected;

  const SharedListSelector({
    super.key,
    required this.sharedLists,
    required this.currentListOwnerId,
    required this.onListSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Select List',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<String?>(
            value: currentListOwnerId,
            decoration: InputDecoration(
              hintText: 'Choose a shared list',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
            items: [
              // Personal list option
              const DropdownMenuItem<String?>(
                value: null,
                child: Row(
                  children: [
                    Icon(Icons.person, size: 20),
                    SizedBox(width: 8),
                    Text('My Personal List'),
                  ],
                ),
              ),
              // Shared lists
              ...sharedLists.map((list) {
                return DropdownMenuItem<String?>(
                  value: list.userId,
                  child: SizedBox(
                    width: double.infinity,
                    child: Row(
                      children: [
                        Icon(
                          Icons.people,
                          size: 20,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Flexible(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                list.name ?? 'Shared List',
                                style: const TextStyle(fontWeight: FontWeight.w500),
                                overflow: TextOverflow.ellipsis,
                              ),
                              if (list.collaboratorCount != null)
                                Text(
                                  '${list.collaboratorCount} collaborators',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                        // Role indicator
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getRoleColor(list.userRole),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            _getRoleText(list.userRole),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ],
            onChanged: onListSelected,
          ),
          // Show current list info
          if (currentListOwnerId != null) ...[
            const SizedBox(height: 12),
            _buildCurrentListInfo(context),
          ],
        ],
      ),
    );
  }

  Widget _buildCurrentListInfo(BuildContext context) {
    final currentList = sharedLists.firstWhere(
      (list) => list.userId == currentListOwnerId,
      orElse: () => sharedLists.first,
    );

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Viewing: ${currentList.name ?? 'Shared List'}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                if (currentList.itemCounts != null)
                  Text(
                    '${currentList.itemCounts!.total} items (${currentList.itemCounts!.unchecked} remaining)',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                Text(
                  'Last activity: ${_formatLastActivity(currentList.lastCollaborativeActivity)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getRoleColor(UserRole? role) {
    switch (role) {
      case UserRole.owner:
        return Colors.purple;
      case UserRole.editor:
        return Colors.blue;
      case UserRole.viewer:
        return Colors.grey;
      default:
        // If role is null, assume owner (this will be fixed when backend properly sets userRole)
        return Colors.purple;
    }
  }

  String _getRoleText(UserRole? role) {
    switch (role) {
      case UserRole.owner:
        return 'OWNER';
      case UserRole.editor:
        return 'EDITOR';
      case UserRole.viewer:
        return 'VIEWER';
      default:
        // If role is null, assume owner (this will be fixed when backend properly sets userRole)
        return 'OWNER';
    }
  }

  String _formatLastActivity(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
