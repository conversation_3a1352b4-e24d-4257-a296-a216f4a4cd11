import { API_ENDPOINTS } from './api';
import api from './api-client';

export interface CalendarConnection {
  id: string;
  provider: 'google' | 'outlook' | 'apple' | 'other';
  displayName: string;
  isConnected: boolean;
  email?: string;
  lastSyncedAt?: string;
  syncEnabled: boolean;
  calendarId?: string;
}

export interface SyncStatus {
  status: 'synced' | 'pending' | 'failed' | 'not_synced';
  lastSyncedAt?: string;
  error?: string;
  eventId?: string;
}

class CalendarService {
  // Initialize Google Calendar OAuth flow
  async connectGoogleCalendar(): Promise<{ authUrl: string }> {
    const response = await api.get(`${API_ENDPOINTS.CALENDAR}/google/authorize`);
    return response.data;
  }

  // Disconnect Google Calendar
  async disconnectGoogleCalendar(): Promise<{ success: boolean }> {
    const response = await api.delete(`${API_ENDPOINTS.CALENDAR}/google`);
    return response.data;
  }

  // Get current sync status for a task
  async getTaskSyncStatus(taskId: string): Promise<SyncStatus> {
    try {
      const response = await api.get(`${API_ENDPOINTS.CALENDAR}/sync/status/${taskId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get sync status:', error);
      return { status: 'not_synced' };
    }
  }

  // Get all connected calendar accounts
  async getConnectedCalendars(): Promise<CalendarConnection[]> {
    try {
      const response = await api.get(`${API_ENDPOINTS.CALENDAR}/connections`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch connected calendars:', error);
      return [];
    }
  }

  // Handle OAuth callback
  async handleOAuthCallback(code: string, state?: string): Promise<{ success: boolean }> {
    try {
      const response = await api.get(`${API_ENDPOINTS.CALENDAR}/google/callback`, {
        params: { code, state }
      });
      return response.data;
    } catch (error) {
      console.error('OAuth callback failed:', error);
      return { success: false };
    }
  }
}

export const calendarService = new CalendarService();
