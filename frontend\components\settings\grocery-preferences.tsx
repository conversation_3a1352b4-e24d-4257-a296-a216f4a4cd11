"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Loader2, ShoppingCart, Users, User } from 'lucide-react';
import { groceryPreferencesService, GroceryPreferences } from '@/lib/grocery-preferences-service';

interface AvailableSharedList {
  id: string;
  ownerName: string;
  ownerEmail: string;
}

export function GroceryPreferencesSettings() {
  const [preferences, setPreferences] = useState<GroceryPreferences>({
    defaultListType: 'personal',
    defaultSharedListOwnerId: null,
    autoSwitchToDefault: false,
    showPersonalListInSidebar: true
  });
  const [availableSharedLists, setAvailableSharedLists] = useState<AvailableSharedList[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  // Load preferences and available shared lists
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        const [prefs, sharedLists] = await Promise.all([
          groceryPreferencesService.getGroceryPreferences(),
          groceryPreferencesService.getAvailableSharedLists()
        ]);
        
        setPreferences(prefs);
        setAvailableSharedLists(sharedLists);
      } catch (error: any) {
        console.error('Error loading grocery preferences:', error);
        toast({
          title: 'Error Loading Preferences',
          description: error.message || 'Failed to load grocery preferences',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [toast]);

  const handleSave = async () => {
    try {
      setIsSaving(true);
      
      // Validate that if defaultListType is 'shared', a shared list is selected
      if (preferences.defaultListType === 'shared' && !preferences.defaultSharedListOwnerId) {
        toast({
          title: 'Validation Error',
          description: 'Please select a shared list when using shared as default',
          variant: 'destructive'
        });
        return;
      }

      await groceryPreferencesService.updateGroceryPreferences(preferences);
      
      toast({
        title: 'Preferences Saved',
        description: 'Your grocery list preferences have been updated successfully'
      });
    } catch (error: any) {
      console.error('Error saving grocery preferences:', error);
      toast({
        title: 'Error Saving Preferences',
        description: error.message || 'Failed to save grocery preferences',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const updatePreference = <K extends keyof GroceryPreferences>(
    key: K,
    value: GroceryPreferences[K]
  ) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Grocery List Preferences
          </CardTitle>
          <CardDescription>
            Configure your default grocery list behavior
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingCart className="h-5 w-5" />
          Grocery List Preferences
        </CardTitle>
        <CardDescription>
          Configure your default grocery list behavior and AI suggestions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Default List Type */}
        <div className="space-y-3">
          <Label htmlFor="defaultListType" className="text-sm font-medium">
            Default List Type
          </Label>
          <Select
            value={preferences.defaultListType}
            onValueChange={(value: 'personal' | 'shared') => updatePreference('defaultListType', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select default list type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="personal">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Personal List
                </div>
              </SelectItem>
              <SelectItem value="shared">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Shared List
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            Choose whether AI quick-add and suggestions should target your personal list or a shared list by default
          </p>
        </div>

        {/* Default Shared List Selection */}
        {preferences.defaultListType === 'shared' && (
          <div className="space-y-3">
            <Label htmlFor="defaultSharedList" className="text-sm font-medium">
              Default Shared List
            </Label>
            <Select
              value={preferences.defaultSharedListOwnerId || ''}
              onValueChange={(value) => updatePreference('defaultSharedListOwnerId', value || null)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a shared list" />
              </SelectTrigger>
              <SelectContent>
                {availableSharedLists.map((list) => (
                  <SelectItem key={list.id} value={list.id}>
                    <div className="flex flex-col">
                      <span className="font-medium">{list.ownerName}</span>
                      <span className="text-xs text-muted-foreground">{list.ownerEmail}</span>
                    </div>
                  </SelectItem>
                ))}
                {availableSharedLists.length === 0 && (
                  <SelectItem value="" disabled>
                    No shared lists available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              Select which shared list should be used as your default target for AI features
            </p>
          </div>
        )}

        {/* Auto Switch to Default */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="autoSwitchToDefault" className="text-sm font-medium">
              Auto-switch to Default List
            </Label>
            <p className="text-xs text-muted-foreground">
              Automatically switch to your default list when opening the grocery page
            </p>
          </div>
          <Switch
            id="autoSwitchToDefault"
            checked={preferences.autoSwitchToDefault}
            onCheckedChange={(checked) => updatePreference('autoSwitchToDefault', checked)}
          />
        </div>

        {/* Show Personal List in Sidebar */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="showPersonalListInSidebar" className="text-sm font-medium">
              Show Personal List in Sidebar
            </Label>
            <p className="text-xs text-muted-foreground">
              Display your personal list option in the shared lists sidebar
            </p>
          </div>
          <Switch
            id="showPersonalListInSidebar"
            checked={preferences.showPersonalListInSidebar}
            onCheckedChange={(checked) => updatePreference('showPersonalListInSidebar', checked)}
          />
        </div>

        {/* Save Button */}
        <div className="pt-4">
          <Button onClick={handleSave} disabled={isSaving} className="w-full">
            {isSaving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              'Save Preferences'
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
