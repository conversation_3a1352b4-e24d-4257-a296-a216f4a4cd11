import 'package:flutter_test/flutter_test.dart';
import 'package:flashtasks_mobile/src/features/groceries/models/grocery_list.dart';

void main() {
  group('GroceryList <PERSON><PERSON><PERSON> Parsing', () {
    test('should parse userId as object correctly', () {
      // This is the actual API response format from the backend
      final json = {
        "_id": "68356d91d76b8edc03aeed38",
        "userId": {
          "_id": "68356d80d76b8edc03aeed24",
          "name": "<PERSON> Viken",
          "email": "<EMAIL>"
        },
        "name": null,
        "isShared": true,
        "collaborators": [
          {
            "userId": {
              "_id": "67f9fd5fe33431ef3dbfa5d7",
              "name": "<PERSON> Viken",
              "email": "<EMAIL>",
              "id": "67f9fd5fe33431ef3dbfa5d7"
            },
            "role": "editor",
            "joinedAt": "2025-05-27T21:16:24.004Z",
            "invitedBy": {
              "_id": "68356d80d76b8edc03aeed24",
              "name": "<PERSON> Viken",
              "email": "<EMAIL>",
              "id": "68356d80d76b8edc03aeed24"
            }
          }
        ],
        "shareSettings": {
          "allowCollaboratorInvites": false,
          "requireApprovalForEdits": false,
          "notifyOnChanges": true
        },
        "lastCollaborativeActivity": "2025-05-27T21:16:24.010Z",
        "createdAt": "2025-05-27T07:45:21.948Z",
        "updatedAt": "2025-05-27T21:16:24.011Z",
        "collaboratorCount": 2,
        "userRole": null
      };

      // This should not throw an exception
      final groceryList = GroceryList.fromJson(json);

      // Verify the userId was extracted correctly
      expect(groceryList.userId, equals("68356d80d76b8edc03aeed24"));
      expect(groceryList.id, equals("68356d91d76b8edc03aeed38"));
      expect(groceryList.isShared, isTrue);
      expect(groceryList.collaborators.length, equals(1));
      expect(groceryList.collaborators.first.userId.id, equals("67f9fd5fe33431ef3dbfa5d7"));
      expect(groceryList.collaborators.first.userId.name, equals("Robin Viken"));
    });

    test('should parse userId as string correctly (backward compatibility)', () {
      final json = {
        "_id": "test-id",
        "userId": "simple-user-id",
        "isShared": false,
        "collaborators": [],
        "shareSettings": {
          "allowCollaboratorInvites": false,
          "requireApprovalForEdits": false,
          "notifyOnChanges": true
        },
        "lastCollaborativeActivity": "2025-05-27T21:16:24.010Z",
        "createdAt": "2025-05-27T07:45:21.948Z",
        "updatedAt": "2025-05-27T21:16:24.011Z"
      };

      final groceryList = GroceryList.fromJson(json);

      expect(groceryList.userId, equals("simple-user-id"));
      expect(groceryList.id, equals("test-id"));
    });

    test('should handle null userId gracefully', () {
      final json = {
        "_id": "test-id",
        "userId": null,
        "isShared": false,
        "collaborators": [],
        "shareSettings": {
          "allowCollaboratorInvites": false,
          "requireApprovalForEdits": false,
          "notifyOnChanges": true
        },
        "lastCollaborativeActivity": "2025-05-27T21:16:24.010Z",
        "createdAt": "2025-05-27T07:45:21.948Z",
        "updatedAt": "2025-05-27T21:16:24.011Z"
      };

      final groceryList = GroceryList.fromJson(json);

      expect(groceryList.userId, equals(""));
      expect(groceryList.id, equals("test-id"));
    });
  });
}
