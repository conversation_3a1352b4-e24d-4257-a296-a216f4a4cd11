import mongoose, { Document, Schema, Types } from 'mongoose';

// Define interface for collaborator
export interface IGroceryCollaborator {
  userId: Types.ObjectId;
  role: 'editor' | 'viewer';
  joinedAt: Date;
  invitedBy: Types.ObjectId;
}

// Define interface for share settings
export interface IGroceryShareSettings {
  allowCollaboratorInvites: boolean;
  requireApprovalForEdits: boolean;
  notifyOnChanges: boolean;
}

// Define interface for GroceryList document
export interface IGroceryList extends Document {
  userId: Types.ObjectId; // Owner of the list
  name?: string; // Optional name for the list
  isShared: boolean;
  collaborators: IGroceryCollaborator[];
  shareSettings: IGroceryShareSettings;
  lastCollaborativeActivity: Date;
  createdAt: Date;
  updatedAt: Date;
  
  // Virtual properties
  collaboratorCount: number;
  
  // Methods
  addCollaborator(userId: Types.ObjectId, role: 'editor' | 'viewer', invitedBy: Types.ObjectId): Promise<void>;
  removeCollaborator(userId: Types.ObjectId): Promise<void>;
  updateCollaboratorRole(userId: Types.ObjectId, newRole: 'editor' | 'viewer'): Promise<void>;
  hasAccess(userId: Types.ObjectId): boolean;
  getUserRole(userId: Types.ObjectId): 'owner' | 'editor' | 'viewer' | null;
  canEdit(userId: Types.ObjectId): boolean;
  canView(userId: Types.ObjectId): boolean;
}

// Define schema for collaborator subdocument
const groceryCollaboratorSchema = new Schema<IGroceryCollaborator>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  role: {
    type: String,
    enum: ['editor', 'viewer'],
    required: true,
  },
  joinedAt: {
    type: Date,
    default: Date.now,
  },
  invitedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
}, { _id: false });

// GroceryList Schema
const groceryListSchema = new Schema<IGroceryList>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      unique: true, // Each user has one grocery list
      index: true,
    },
    name: {
      type: String,
      trim: true,
      maxlength: [100, 'List name cannot exceed 100 characters'],
    },
    isShared: {
      type: Boolean,
      default: false,
      index: true,
    },
    collaborators: [groceryCollaboratorSchema],
    shareSettings: {
      allowCollaboratorInvites: {
        type: Boolean,
        default: false,
      },
      requireApprovalForEdits: {
        type: Boolean,
        default: false,
      },
      notifyOnChanges: {
        type: Boolean,
        default: true,
      },
    },
    lastCollaborativeActivity: {
      type: Date,
      default: Date.now,
      index: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes for efficient querying
groceryListSchema.index({ userId: 1 });
groceryListSchema.index({ 'collaborators.userId': 1, isShared: 1 });
groceryListSchema.index({ lastCollaborativeActivity: -1 });

// Virtual for collaborator count
groceryListSchema.virtual('collaboratorCount').get(function() {
  return this.collaborators.length + 1; // +1 for owner
});

// Method to add a collaborator to the list
groceryListSchema.methods.addCollaborator = async function(
  userId: Types.ObjectId, 
  role: 'editor' | 'viewer', 
  invitedBy: Types.ObjectId
): Promise<void> {
  // Check if user is already a collaborator
  const existingCollaborator = this.collaborators.find((collaborator: IGroceryCollaborator) => 
    collaborator.userId.toString() === userId.toString()
  );
  
  if (existingCollaborator) {
    throw new Error('User is already a collaborator on this list');
  }
  
  // Cannot add the owner as a collaborator
  if (this.userId.toString() === userId.toString()) {
    throw new Error('Cannot add the owner as a collaborator');
  }
  
  // Add the new collaborator
  this.collaborators.push({
    userId,
    role,
    joinedAt: new Date(),
    invitedBy,
  });
  
  this.lastCollaborativeActivity = new Date();
  await this.save();
};

// Method to remove a collaborator from the list
groceryListSchema.methods.removeCollaborator = async function(userId: Types.ObjectId): Promise<void> {
  // Cannot remove the owner
  if (this.userId.toString() === userId.toString()) {
    throw new Error('Cannot remove the owner from the list');
  }
  
  const initialLength = this.collaborators.length;
  this.collaborators = this.collaborators.filter((collaborator: IGroceryCollaborator) => 
    collaborator.userId.toString() !== userId.toString()
  );
  
  if (this.collaborators.length === initialLength) {
    throw new Error('User is not a collaborator on this list');
  }
  
  this.lastCollaborativeActivity = new Date();
  await this.save();
};

// Method to update a collaborator's role
groceryListSchema.methods.updateCollaboratorRole = async function(
  userId: Types.ObjectId, 
  newRole: 'editor' | 'viewer'
): Promise<void> {
  // Cannot change owner's role
  if (this.userId.toString() === userId.toString()) {
    throw new Error('Cannot change the owner\'s role');
  }
  
  const collaborator = this.collaborators.find((collaborator: IGroceryCollaborator) => 
    collaborator.userId.toString() === userId.toString()
  );
  
  if (!collaborator) {
    throw new Error('User is not a collaborator on this list');
  }
  
  collaborator.role = newRole;
  this.lastCollaborativeActivity = new Date();
  await this.save();
};

// Method to check if a user has access to the list
groceryListSchema.methods.hasAccess = function(userId: Types.ObjectId): boolean {
  if (this.userId.toString() === userId.toString()) {
    return true;
  }
  
  return this.collaborators.some((collaborator: IGroceryCollaborator) => 
    collaborator.userId.toString() === userId.toString()
  );
};

// Method to get a user's role in the list
groceryListSchema.methods.getUserRole = function(userId: Types.ObjectId): 'owner' | 'editor' | 'viewer' | null {
  if (this.userId.toString() === userId.toString()) {
    return 'owner';
  }
  
  const collaborator = this.collaborators.find((collaborator: IGroceryCollaborator) => 
    collaborator.userId.toString() === userId.toString()
  );
  
  return collaborator ? collaborator.role : null;
};

// Method to check if a user can edit the list
groceryListSchema.methods.canEdit = function(userId: Types.ObjectId): boolean {
  const role = this.getUserRole(userId);
  return role === 'owner' || role === 'editor';
};

// Method to check if a user can view the list
groceryListSchema.methods.canView = function(userId: Types.ObjectId): boolean {
  return this.hasAccess(userId);
};

export const GroceryList = mongoose.model<IGroceryList>('GroceryList', groceryListSchema);
