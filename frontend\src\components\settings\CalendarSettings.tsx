import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { <PERSON>, <PERSON><PERSON>, Alert, Spinner } from '@/components/ui';
import { api } from '../../services/api';

export const CalendarSettings: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleConnect = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.get('/api/auth/google/calendar/authorize');
      if (response.data.success && response.data.data.authUrl) {
        window.location.href = response.data.data.authUrl;
      } else {
        throw new Error('Failed to get authorization URL');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to connect to Google Calendar');
    } finally {
      setLoading(false);
    }
  };

  const handleDisconnect = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await api.delete('/api/auth/google/calendar');
      window.location.reload(); // Refresh to update user state
    } catch (err: any) {
      setError(err.message || 'Failed to disconnect Google Calendar');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold mb-4">Calendar Integration</h2>
      
      {error && (
        <Alert variant="error" className="mb-4">
          {error}
        </Alert>
      )}

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium">Google Calendar</h3>
            <p className="text-sm text-gray-500">
              {user?.googleCalendarSyncEnabled
                ? 'Your tasks with deadlines will sync to Google Calendar'
                : 'Connect your Google Calendar to sync tasks with deadlines'}
            </p>
          </div>

          {loading ? (
            <Spinner size="sm" />
          ) : user?.googleCalendarSyncEnabled ? (
            <Button
              variant="outline"
              color="red"
              onClick={handleDisconnect}
            >
              Disconnect
            </Button>
          ) : (
            <Button
              variant="outline"
              color="primary"
              onClick={handleConnect}
            >
              Connect
            </Button>
          )}
        </div>

        {user?.googleCalendarSyncEnabled && (
          <div className="text-sm text-gray-500">
            Last synced: {user.lastGoogleCalendarSync
              ? new Date(user.lastGoogleCalendarSync).toLocaleString()
              : 'Never'}
          </div>
        )}
      </div>
    </Card>
  );
};
