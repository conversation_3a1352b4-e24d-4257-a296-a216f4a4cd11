import express from 'express';
import { protect } from '../../middleware/auth';
import { googleCalendarService } from '../../services/googleCalendarService';
import { User } from '../../models/User';
import secureLogger from '../../utils/secureLogger';

// Extend Express Request type to include session
declare global {
  namespace Express {
    interface Request {
      session?: {
        userId?: string;
        [key: string]: any;
      };
    }
  }
}

const router = express.Router();

// @route   GET /api/auth/google/calendar/authorize
// @desc    Get Google Calendar authorization URL
// @access  Private
router.get('/google/calendar/authorize', protect, async (req, res) => {
  try {
    const authUrl = await googleCalendarService.getAuthUrl();
    res.json({ success: true, data: { authUrl } });
    return;
  } catch (error: any) {
    secureLogger.error('[CalendarAuth] Failed to get auth URL:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to generate authorization URL' }
    });
    return;
  }
});

// @route   GET /api/auth/google/callback/calendar
// @desc    Handle Google Calendar OAuth callback
// @access  Public (but requires valid OAuth state)
router.get('/google/callback/calendar', async (req, res) => {
  const { code, state } = req.query;
  const userId = req.session?.userId; // Assuming you store userId in session

  if (!code || !userId) {
    return res.status(400).json({
      success: false,
      error: { message: 'Missing required parameters' }
    });
  }

  try {
    const tokens = await googleCalendarService.handleAuthCallback(code as string);
    if (!tokens) {
      throw new Error('Failed to get tokens');
    }

    // Update user with calendar tokens
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    user.googleCalendarTokens = {
      accessToken: tokens.access_token!,
      refreshToken: tokens.refresh_token || undefined,
      expiryDate: tokens.expiry_date || Date.now() + 3600000, // Default to 1 hour from now
      scope: tokens.scope || '',
    };
    user.googleCalendarSyncEnabled = true;
    user.googleCalendarId = 'primary'; // Default to primary calendar
    await user.save();

    // Redirect to frontend settings page
    res.redirect(`${process.env.FRONTEND_URL}/settings/integrations?success=google-calendar`);
    return;
  } catch (error: any) {
    secureLogger.error('[CalendarAuth] OAuth callback error:', error);
    res.redirect(`${process.env.FRONTEND_URL}/settings/integrations?error=google-calendar`);
    return;
  }
});

// @route   DELETE /api/auth/google/calendar
// @desc    Disconnect Google Calendar
// @access  Private
router.delete('/google/calendar', protect, async (req, res) => {
  try {
    const user = await User.findById(req.user?.userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' }
      });
    }

    // Clear calendar tokens and disable sync
    user.googleCalendarTokens = undefined;
    user.googleCalendarSyncEnabled = false;
    user.googleCalendarId = undefined;
    await user.save();

    res.json({ success: true });
    return;
  } catch (error: any) {
    secureLogger.error('[CalendarAuth] Failed to disconnect calendar:', error);
    res.status(500).json({
      success: false,
      error: { message: 'Failed to disconnect calendar' }
    });
    return;
  }
});

export default router;
