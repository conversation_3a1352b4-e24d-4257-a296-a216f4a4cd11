'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useGrocery } from '@/lib/grocery-context';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Users,
  UserPlus,
  Settings,
  Share2,
  ShieldCheck,
  Bell,
  Trash2,
  Crown,
  Edit,
  Eye
} from 'lucide-react';

interface GroceryCollaborationPanelProps {
  className?: string;
}

// Utility function to get initials from name or email
const getInitials = (name?: string, email?: string): string => {
  if (name) {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  }
  if (email) {
    return email.slice(0, 2).toUpperCase();
  }
  return 'U';
};

export function GroceryCollaborationPanel({ className }: GroceryCollaborationPanelProps) {
  const {
    groceryList,
    isShared,
    collaborators,
    shareSettings,
    userRole,
    isLoadingCollaboration,
    enableSharing,
    disableSharing,
    updateShareSettings,
    addCollaborator,
    removeCollaborator
  } = useGrocery();

  const { toast } = useToast();
  const [isAddCollaboratorOpen, setIsAddCollaboratorOpen] = useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [newCollaboratorEmail, setNewCollaboratorEmail] = useState('');
  const [newCollaboratorRole, setNewCollaboratorRole] = useState<'editor' | 'viewer'>('editor');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Local state for settings
  const [localSettings, setLocalSettings] = useState({
    allowCollaboratorInvites: shareSettings?.allowCollaboratorInvites ?? false,
    requireApprovalForEdits: shareSettings?.requireApprovalForEdits ?? false,
    notifyOnChanges: shareSettings?.notifyOnChanges ?? true,
  });

  const handleEnableSharing = async () => {
    try {
      setIsSubmitting(true);
      await enableSharing(localSettings);
      toast({
        title: 'Sharing Enabled',
        description: 'Your grocery list is now shared and ready for collaboration.',
      });
    } catch (error) {
      console.error('Failed to enable sharing:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDisableSharing = async () => {
    try {
      setIsSubmitting(true);
      await disableSharing();
      toast({
        title: 'Sharing Disabled',
        description: 'Your grocery list is no longer shared.',
      });
    } catch (error) {
      console.error('Failed to disable sharing:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateSettings = async () => {
    try {
      setIsSubmitting(true);
      await updateShareSettings(localSettings);
      setIsSettingsOpen(false);
      toast({
        title: 'Settings Updated',
        description: 'Share settings have been updated successfully.',
      });
    } catch (error) {
      console.error('Failed to update settings:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddCollaborator = async () => {
    if (!newCollaboratorEmail.trim()) {
      toast({
        title: 'Email Required',
        description: 'Please enter a valid email address.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await addCollaborator(newCollaboratorEmail.trim(), newCollaboratorRole);
      setNewCollaboratorEmail('');
      setNewCollaboratorRole('editor');
      setIsAddCollaboratorOpen(false);
      toast({
        title: 'Collaborator Added',
        description: `${newCollaboratorEmail} has been added to your grocery list.`,
      });
    } catch (error) {
      console.error('Failed to add collaborator:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemoveCollaborator = async (collaboratorId: string) => {
    try {
      setIsSubmitting(true);
      await removeCollaborator(collaboratorId);
      toast({
        title: 'Collaborator Removed',
        description: 'The collaborator has been removed from your grocery list.',
      });
    } catch (error) {
      console.error('Failed to remove collaborator:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'editor':
        return <Edit className="h-4 w-4 text-blue-500" />;
      case 'viewer':
        return <Eye className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'owner':
        return 'default';
      case 'editor':
        return 'secondary';
      case 'viewer':
        return 'outline';
      default:
        return 'outline';
    }
  };

  if (isLoadingCollaboration) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Collaboration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Collaboration
        </CardTitle>
        <CardDescription>
          {isShared
            ? 'Manage who can access and edit your grocery list'
            : 'Share your grocery list with family and friends'
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {!isShared ? (
          <div className="text-center space-y-4">
            <div className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="allow-invites">Allow collaborator invites</Label>
                  <Switch
                    id="allow-invites"
                    checked={localSettings.allowCollaboratorInvites}
                    onCheckedChange={(checked) =>
                      setLocalSettings(prev => ({ ...prev, allowCollaboratorInvites: checked }))
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="require-approval">Require approval for edits</Label>
                  <Switch
                    id="require-approval"
                    checked={localSettings.requireApprovalForEdits}
                    onCheckedChange={(checked) =>
                      setLocalSettings(prev => ({ ...prev, requireApprovalForEdits: checked }))
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="notify-changes">Notify on changes</Label>
                  <Switch
                    id="notify-changes"
                    checked={localSettings.notifyOnChanges}
                    onCheckedChange={(checked) =>
                      setLocalSettings(prev => ({ ...prev, notifyOnChanges: checked }))
                    }
                  />
                </div>
              </div>
            </div>
            <Button
              onClick={handleEnableSharing}
              disabled={isSubmitting}
              className="w-full"
            >
              <Share2 className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Enabling...' : 'Enable Sharing'}
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Sharing Status */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Share2 className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Sharing is enabled</span>
              </div>
              <div className="flex gap-2">
                <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Share Settings</DialogTitle>
                      <DialogDescription>
                        Configure how your grocery list is shared
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="allow-invites-dialog">Allow collaborator invites</Label>
                        <Switch
                          id="allow-invites-dialog"
                          checked={localSettings.allowCollaboratorInvites}
                          onCheckedChange={(checked) =>
                            setLocalSettings(prev => ({ ...prev, allowCollaboratorInvites: checked }))
                          }
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="require-approval-dialog">Require approval for edits</Label>
                        <Switch
                          id="require-approval-dialog"
                          checked={localSettings.requireApprovalForEdits}
                          onCheckedChange={(checked) =>
                            setLocalSettings(prev => ({ ...prev, requireApprovalForEdits: checked }))
                          }
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="notify-changes-dialog">Notify on changes</Label>
                        <Switch
                          id="notify-changes-dialog"
                          checked={localSettings.notifyOnChanges}
                          onCheckedChange={(checked) =>
                            setLocalSettings(prev => ({ ...prev, notifyOnChanges: checked }))
                          }
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsSettingsOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleUpdateSettings} disabled={isSubmitting}>
                        {isSubmitting ? 'Saving...' : 'Save Changes'}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      Disable
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Disable Sharing</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will remove all collaborators and make your grocery list private.
                        This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={handleDisableSharing} disabled={isSubmitting}>
                        {isSubmitting ? 'Disabling...' : 'Disable Sharing'}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>

            <Separator />

            {/* Collaborators List */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">
                  Collaborators ({collaborators.length + 1})
                </h4>
                <Dialog open={isAddCollaboratorOpen} onOpenChange={setIsAddCollaboratorOpen}>
                  <DialogTrigger asChild>
                    <Button size="sm">
                      <UserPlus className="h-4 w-4 mr-2" />
                      Add
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Add Collaborator</DialogTitle>
                      <DialogDescription>
                        Invite someone to collaborate on your grocery list
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="email">Email address</Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="Enter email address"
                          value={newCollaboratorEmail}
                          onChange={(e) => setNewCollaboratorEmail(e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="role">Role</Label>
                        <Select value={newCollaboratorRole} onValueChange={(value: 'editor' | 'viewer') => setNewCollaboratorRole(value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="editor">Editor - Can add and edit items</SelectItem>
                            <SelectItem value="viewer">Viewer - Can only view items</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setIsAddCollaboratorOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleAddCollaborator} disabled={isSubmitting}>
                        {isSubmitting ? 'Adding...' : 'Add Collaborator'}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>

              <div className="space-y-3">
                {/* Owner (current user) */}
                <div className="flex items-center justify-between p-3 rounded-lg border">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>
                        {getInitials("You")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">You</span>
                        {getRoleIcon('owner')}
                      </div>
                      <span className="text-xs text-muted-foreground">List owner</span>
                    </div>
                  </div>
                  <Badge variant={getRoleBadgeVariant('owner')}>Owner</Badge>
                </div>

                {/* Collaborators */}
                {collaborators.map((collaborator) => (
                  <div key={collaborator.userId._id || collaborator.userId.email} className="flex items-center justify-between p-3 rounded-lg border">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>
                          {getInitials(collaborator.userId.name, collaborator.userId.email)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">
                            {collaborator.userId.name || collaborator.userId.email}
                          </span>
                          {getRoleIcon(collaborator.role)}
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {collaborator.userId.email}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getRoleBadgeVariant(collaborator.role)}>
                        {collaborator.role}
                      </Badge>
                      {userRole === 'owner' && (
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Remove Collaborator</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to remove {collaborator.userId.name || collaborator.userId.email} from your grocery list?
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleRemoveCollaborator(collaborator.userId._id)}
                                disabled={isSubmitting}
                              >
                                {isSubmitting ? 'Removing...' : 'Remove'}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
