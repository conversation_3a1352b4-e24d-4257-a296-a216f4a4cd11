import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import '../../../core/services/grocery_service.dart';

/// Widget for quickly adding grocery items
class GroceryQuickAddWidget extends ConsumerStatefulWidget {
  final Function(String name, {String? quantity, String? categoryName}) onAddItem;

  const GroceryQuickAddWidget({
    super.key,
    required this.onAddItem,
  });

  @override
  ConsumerState<GroceryQuickAddWidget> createState() => _GroceryQuickAddWidgetState();
}

class _GroceryQuickAddWidgetState extends ConsumerState<GroceryQuickAddWidget> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  
  List<String> _suggestions = [];
  bool _isLoading = false;
  String? _error;

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  // Handle text changes and fetch suggestions
  void _onTextChanged(String text) {
    if (text.length < 2) {
      setState(() {
        _suggestions = [];
      });
      return;
    }

    _fetchSuggestions(text);
  }

  // Fetch suggestions from the API
  Future<void> _fetchSuggestions(String query) async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final groceryService = ref.read(groceryServiceProvider);
      final results = await groceryService.searchGroceryLibrary(query);

      setState(() {
        _suggestions = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  // Handle adding an item
  void _handleAdd() {
    final text = _controller.text.trim();
    if (text.isNotEmpty) {
      widget.onAddItem(text);
      _controller.clear();
      setState(() {
        _suggestions = [];
      });
    }
  }

  // Handle selecting a suggestion
  void _selectSuggestion(String suggestion) {
    widget.onAddItem(suggestion);
    _controller.clear();
    setState(() {
      _suggestions = [];
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.3),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Input field with add button
          Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              children: [
                Icon(
                  LucideIcons.plus,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextField(
                    controller: _controller,
                    focusNode: _focusNode,
                    decoration: InputDecoration(
                      hintText: 'Add grocery item...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: theme.colorScheme.outline,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: theme.colorScheme.primary,
                          width: 2,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      suffixIcon: _controller.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(LucideIcons.x, size: 16),
                              onPressed: () {
                                _controller.clear();
                                setState(() {
                                  _suggestions = [];
                                });
                              },
                            )
                          : null,
                    ),
                    onChanged: _onTextChanged,
                    onSubmitted: (_) => _handleAdd(),
                    textInputAction: TextInputAction.done,
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _controller.text.trim().isNotEmpty ? _handleAdd : null,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Add'),
                ),
              ],
            ),
          ),

          // Suggestions
          if (_isLoading || _error != null || _suggestions.isNotEmpty)
            Container(
              constraints: const BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: theme.dividerColor,
                  ),
                ),
              ),
              child: _buildSuggestionsList(),
            ),
        ],
      ),
    );
  }

  Widget _buildSuggestionsList() {
    if (_isLoading) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      );
    }

    if (_error != null) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Text(
          'Error: $_error',
          style: TextStyle(
            color: Theme.of(context).colorScheme.error,
            fontSize: 12,
          ),
        ),
      );
    }

    if (_suggestions.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Text(
          'No suggestions found',
          style: TextStyle(
            fontStyle: FontStyle.italic,
            fontSize: 12,
          ),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      itemCount: _suggestions.length,
      itemBuilder: (context, index) {
        final suggestion = _suggestions[index];
        return ListTile(
          dense: true,
          leading: Icon(
            LucideIcons.search,
            size: 16,
            color: Theme.of(context).colorScheme.primary,
          ),
          title: Text(
            suggestion,
            style: const TextStyle(fontSize: 14),
          ),
          onTap: () => _selectSuggestion(suggestion),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 4,
          ),
        );
      },
    );
  }
}
